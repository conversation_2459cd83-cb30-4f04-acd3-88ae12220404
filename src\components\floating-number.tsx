'use client';

import { motion } from 'motion/react';

const FloatingNumber = ({
  number,
  position,
  delay,
  size = 'text-6xl',
  color = 'text-cyan-400',
}: {
  number: string;
  position: string;
  delay: number;
  size?: string;
  color?: string;
}) => (
  <motion.div
    className={`absolute ${position} ${size} ${color} pointer-events-none font-black opacity-50 select-none dark:text-cyan-300 dark:opacity-40`}
    initial={{ opacity: 0, scale: 0 }}
    animate={{
      opacity: [0, 0.5, 0.3, 0.5],
      scale: [0, 1.1, 0.9, 1],
      rotate: [0, 10, -10, 5, 0],
    }}
    transition={{
      duration: 15,
      delay,
      repeat: Infinity,
      ease: 'linear',
    }}
  >
    {number}
  </motion.div>
);

export default FloatingNumber;
