'use client';

import React from 'react';
import { motion } from 'motion/react';

const LoadingSkeleton = () => {
  return (
    <div className="from-background via-muted/30 to-background min-h-screen bg-gradient-to-br">
      {/* Hero skeleton */}
      <section className="relative flex min-h-[80vh] items-center justify-center px-4 py-20">
        <div className="mx-auto max-w-4xl text-center">
          <motion.div
            animate={{ opacity: [0.5, 1, 0.5] }}
            transition={{ duration: 2, repeat: Infinity, ease: 'easeInOut' }}
            className="mx-auto mb-6 h-16 w-3/4 rounded-lg bg-gray-200 dark:bg-gray-700"
          />
          <motion.div
            animate={{ opacity: [0.5, 1, 0.5] }}
            transition={{
              duration: 2,
              repeat: Infinity,
              ease: 'easeInOut',
              delay: 0.2,
            }}
            className="mx-auto mb-6 h-8 w-1/2 rounded-lg bg-gray-200 dark:bg-gray-700"
          />
          <motion.div
            animate={{ opacity: [0.5, 1, 0.5] }}
            transition={{
              duration: 2,
              repeat: Infinity,
              ease: 'easeInOut',
              delay: 0.4,
            }}
            className="mx-auto mb-12 h-6 w-2/3 rounded-lg bg-gray-200 dark:bg-gray-700"
          />
          <div className="flex flex-col justify-center gap-4 sm:flex-row">
            <motion.div
              animate={{ opacity: [0.5, 1, 0.5] }}
              transition={{
                duration: 2,
                repeat: Infinity,
                ease: 'easeInOut',
                delay: 0.6,
              }}
              className="h-12 w-40 rounded-lg bg-gray-200 dark:bg-gray-700"
            />
            <motion.div
              animate={{ opacity: [0.5, 1, 0.5] }}
              transition={{
                duration: 2,
                repeat: Infinity,
                ease: 'easeInOut',
                delay: 0.8,
              }}
              className="h-12 w-40 rounded-lg bg-gray-200 dark:bg-gray-700"
            />
          </div>
        </div>
      </section>

      {/* Content sections skeleton */}
      {[...Array(6)].map((_, index) => (
        <section key={index} className="px-4 py-20">
          <div className="mx-auto max-w-7xl">
            <div className="mb-16 text-center">
              <motion.div
                animate={{ opacity: [0.5, 1, 0.5] }}
                transition={{
                  duration: 2,
                  repeat: Infinity,
                  ease: 'easeInOut',
                  delay: index * 0.1,
                }}
                className="mx-auto mb-4 h-12 w-1/3 rounded-lg bg-gray-200 dark:bg-gray-700"
              />
              <motion.div
                animate={{ opacity: [0.5, 1, 0.5] }}
                transition={{
                  duration: 2,
                  repeat: Infinity,
                  ease: 'easeInOut',
                  delay: index * 0.1 + 0.2,
                }}
                className="mx-auto h-6 w-1/2 rounded-lg bg-gray-200 dark:bg-gray-700"
              />
            </div>

            {/* Grid skeleton */}
            <div className="grid gap-6 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4">
              {[...Array(4)].map((_, cardIndex) => (
                <motion.div
                  key={cardIndex}
                  animate={{ opacity: [0.5, 1, 0.5] }}
                  transition={{
                    duration: 2,
                    repeat: Infinity,
                    ease: 'easeInOut',
                    delay: index * 0.1 + cardIndex * 0.1,
                  }}
                  className="h-64 rounded-lg bg-gray-200 dark:bg-gray-700"
                />
              ))}
            </div>
          </div>
        </section>
      ))}
    </div>
  );
};

export default LoadingSkeleton;
