'use client';

import React, { useState, useEffect } from 'react';
import { useTranslation, Trans } from 'react-i18next';
import { motion, useInView } from 'motion/react';
import { Calendar, Users, FileText, Target } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import GradientHighlighter from '@/components/gradient-highlighter';

const StatsSection = () => {
  const { t } = useTranslation('about');
  const ref = React.useRef(null);
  const inView = useInView(ref, { amount: 0.3, once: true });

  const stats = [
    {
      key: 'experience',
      icon: Calendar,
      color: 'text-blue-600 dark:text-blue-400',
      bgColor: 'bg-blue-100 dark:bg-blue-900/30',
      gradient: 'from-blue-500/20 to-blue-600/20',
      targetValue: 15,
      suffix: '+',
    },
    {
      key: 'clients',
      icon: Users,
      color: 'text-green-600 dark:text-green-400',
      bgColor: 'bg-green-100 dark:bg-green-900/30',
      gradient: 'from-green-500/20 to-green-600/20',
      targetValue: 500,
      suffix: '+',
    },
    {
      key: 'documents',
      icon: FileText,
      color: 'text-purple-600 dark:text-purple-400',
      bgColor: 'bg-purple-100 dark:bg-purple-900/30',
      gradient: 'from-purple-500/20 to-purple-600/20',
      targetValue: 10,
      suffix: 'M+',
    },
    {
      key: 'accuracy',
      icon: Target,
      color: 'text-orange-600 dark:text-orange-400',
      bgColor: 'bg-orange-100 dark:bg-orange-900/30',
      gradient: 'from-orange-500/20 to-orange-600/20',
      targetValue: 99.9,
      suffix: '%',
    },
  ];

  // Optimized animated counter hook with performance improvements
  const useCounter = (target: number, duration: number = 2000) => {
    const [count, setCount] = useState(0);

    useEffect(() => {
      if (!inView) return;

      let animationId: number;
      let startTime: number;

      const animate = (currentTime: number) => {
        if (!startTime) startTime = currentTime;
        const progress = Math.min((currentTime - startTime) / duration, 1);

        // Optimized easing function for smooth animation
        const easeOutQuart = 1 - Math.pow(1 - progress, 4);
        setCount(target * easeOutQuart);

        if (progress < 1) {
          animationId = requestAnimationFrame(animate);
        } else {
          setCount(target);
        }
      };

      animationId = requestAnimationFrame(animate);

      // Cleanup function to cancel animation if component unmounts
      return () => {
        if (animationId) {
          cancelAnimationFrame(animationId);
        }
      };
    }, [target, duration]);

    return count;
  };

  // Call useCounter for each stat individually to avoid React Hook rules issues
  const counter0 = useCounter(stats[0].targetValue, 2000);
  const counter1 = useCounter(stats[1].targetValue, 2200);
  const counter2 = useCounter(stats[2].targetValue, 2400);
  const counter3 = useCounter(stats[3].targetValue, 2600);

  // Store counters in an array for easy access in the JSX
  const counters = [counter0, counter1, counter2, counter3];

  return (
    <section className="from-muted/30 to-background bg-gradient-to-b px-4 py-20">
      <div className="mx-auto max-w-7xl">
        {/* Section header */}
        <div className="mb-16 text-center">
          <Trans
            i18nKey="stats.title"
            ns="about"
            parent={motion.h2}
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true, margin: '-100px' }}
            transition={{ duration: 0.8 }}
            className="mb-4 text-3xl font-bold text-gray-900 text-shadow-2xs sm:text-4xl md:text-5xl dark:text-white"
            components={{
              highlighted: <GradientHighlighter />,
            }}
          />
          <motion.p
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true, margin: '-100px' }}
            transition={{ duration: 0.8, delay: 0.2 }}
            className="text-primary text-xl font-semibold text-shadow-xs"
          >
            {t('stats.subtitle')}
          </motion.p>
        </div>

        {/* Stats grid */}
        <div ref={ref} className="grid gap-8 sm:grid-cols-2 lg:grid-cols-4">
          {stats.map((stat, index) => {
            const IconComponent = stat.icon;
            const count = counters[index];

            return (
              <motion.div
                key={stat.key}
                initial={{ opacity: 0, y: 50, scale: 0.9 }}
                whileInView={{ opacity: 1, y: 0, scale: 1 }}
                viewport={{ once: true, margin: '-100px' }}
                transition={{ duration: 0.8, delay: index * 0.2 }}
                whileHover={{ y: -8, scale: 1.05 }}
                className="group"
              >
                <Card className="border-border/50 bg-card/80 group-hover:border-primary/30 relative h-full overflow-hidden backdrop-blur-sm transition-all duration-500 hover:shadow-2xl">
                  {/* Background gradient */}
                  <div
                    className={`absolute inset-0 bg-gradient-to-br ${stat.gradient} opacity-0 transition-opacity duration-500 group-hover:opacity-100`}
                  />

                  <CardContent className="relative z-10 p-8 text-center">
                    {/* Icon */}
                    <motion.div
                      className={`mx-auto mb-6 h-20 w-20 rounded-2xl ${stat.bgColor} flex items-center justify-center transition-all duration-300 group-hover:scale-110 group-hover:rotate-6`}
                      whileHover={{ rotate: 12, scale: 1.2 }}
                    >
                      <IconComponent className={`h-10 w-10 ${stat.color}`} />
                    </motion.div>

                    {/* Animated number */}
                    <div className="mb-4">
                      <motion.div
                        className="text-4xl font-bold text-gray-900 md:text-5xl dark:text-white"
                        initial={{ scale: 0.5 }}
                        whileInView={{ scale: 1 }}
                        viewport={{ once: true }}
                        transition={{ duration: 0.5, delay: index * 0.1 }}
                      >
                        {stat.key === 'accuracy'
                          ? count.toFixed(1)
                          : Math.floor(count)}
                        <span className={`${stat.color} ml-1`}>
                          {stat.suffix}
                        </span>
                      </motion.div>
                    </div>

                    {/* Label */}
                    <h3 className="mb-2 text-xl font-semibold text-gray-900 dark:text-white">
                      {t(`stats.items.${stat.key}.label`)}
                    </h3>

                    {/* Description */}
                    <p className="leading-relaxed text-gray-600 dark:text-gray-400">
                      {t(`stats.items.${stat.key}.description`)}
                    </p>

                    {/* Progress bar animation */}
                    <div className="mt-6">
                      <div className="h-1 overflow-hidden rounded-full bg-gray-200 dark:bg-gray-700">
                        <motion.div
                          className={`from-primary to-secondary h-full bg-gradient-to-r`}
                          initial={{ width: 0 }}
                          whileInView={{ width: '100%' }}
                          viewport={{ once: true }}
                          transition={{ duration: 1.5, delay: index * 0.2 }}
                        />
                      </div>
                    </div>
                  </CardContent>

                  {/* Floating particles effect */}
                  <div className="pointer-events-none absolute inset-0">
                    {[...Array(3)].map((_, i) => (
                      <motion.div
                        key={i}
                        className={`absolute h-2 w-2 ${stat.bgColor} rounded-full opacity-0 group-hover:opacity-60`}
                        style={{
                          left: `${20 + i * 30}%`,
                          top: `${30 + i * 20}%`,
                        }}
                        animate={{
                          y: [0, -20, 0],
                          x: [0, 10, 0],
                          opacity: inView ? [0, 0.6, 0] : 0,
                        }}
                        transition={{
                          duration: 3,
                          repeat: Infinity,
                          delay: i * 0.5 + index * 0.2,
                          ease: 'easeInOut',
                        }}
                      />
                    ))}
                  </div>
                </Card>
              </motion.div>
            );
          })}
        </div>

        {/* Bottom achievement showcase */}
        <motion.div
          initial={{ opacity: 0, y: 50 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true, margin: '-100px' }}
          transition={{ duration: 0.8, delay: 0.8 }}
          className="mt-16 text-center"
        >
          <Card className="border-primary/20 from-primary/5 to-secondary/5 overflow-hidden bg-gradient-to-br">
            <CardContent className="p-8">
              <h3 className="mb-4 text-2xl font-bold text-gray-900 dark:text-white">
                Trusted by Industry Leaders
              </h3>
              <p className="mx-auto mb-6 max-w-2xl text-gray-700 dark:text-gray-300">
                Our track record speaks for itself. From small businesses to
                enterprise corporations, we&apos;ve consistently delivered
                exceptional results that drive business growth.
              </p>

              {/* Achievement badges */}
              <div className="flex flex-wrap justify-center gap-4">
                <motion.div
                  whileHover={{ scale: 1.05 }}
                  className="rounded-full bg-blue-100 px-4 py-2 font-semibold text-blue-700 dark:bg-blue-900/30 dark:text-blue-300"
                >
                  ISO 27001 Certified
                </motion.div>
                <motion.div
                  whileHover={{ scale: 1.05 }}
                  className="rounded-full bg-green-100 px-4 py-2 font-semibold text-green-700 dark:bg-green-900/30 dark:text-green-300"
                >
                  GDPR Compliant
                </motion.div>
                <motion.div
                  whileHover={{ scale: 1.05 }}
                  className="rounded-full bg-purple-100 px-4 py-2 font-semibold text-purple-700 dark:bg-purple-900/30 dark:text-purple-300"
                >
                  Enterprise Ready
                </motion.div>
              </div>
            </CardContent>
          </Card>
        </motion.div>
      </div>
    </section>
  );
};

export default StatsSection;
