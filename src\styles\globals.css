@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

/* 
  Defines custom properties that will be available in Tailwind's theme.
  This allows using CSS variables directly in utility classes, e.g., bg-color-background.
*/
@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
  --color-sidebar-ring: var(--sidebar-ring);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar: var(--sidebar);
  --color-chart-5: var(--chart-5);
  --color-chart-4: var(--chart-4);
  --color-chart-3: var(--chart-3);
  --color-chart-2: var(--chart-2);
  --color-chart-1: var(--chart-1);
  --color-ring: var(--ring);
  --color-input: var(--input);
  --color-border: var(--border);
  --color-destructive: var(--destructive);
  --color-accent-foreground: var(--accent-foreground);
  --color-accent: var(--accent);
  --color-muted-foreground: var(--muted-foreground);
  --color-muted: var(--muted);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-secondary: var(--secondary);
  --color-primary-foreground: var(--primary-foreground);
  --color-primary: var(--primary);
  --color-popover-foreground: var(--popover-foreground);
  --color-popover: var(--popover);
  --color-card-foreground: var(--card-foreground);
  --color-card: var(--card);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
}

/* 
  Light Theme Definitions
*/
:root {
  /* Radius */
  --radius: 0.625rem;
  
  /* Core Colors */
  --background: oklch(0.984 0.003 247.858); /* gray-50 */
  --foreground: oklch(0.208 0.042 265.755); /* gray-900 (Primary Text) */
  --card: oklch(1 0 0); /* white (Surface) */
  --card-foreground: oklch(0.208 0.042 265.755); /* gray-900 */
  --popover: oklch(1 0 0); /* white */
  --popover-foreground: oklch(0.208 0.042 265.755); /* gray-900 */
  
  /* Brand Colors */
  --primary: oklch(0.608 0.279 245.831); /* blue-600 */
  --primary-foreground: oklch(1 0 0); /* white */
  --secondary: oklch(0.714 0.123 238.75); /* sky-500 */
  --secondary-foreground: oklch(0.208 0.042 265.755); /* gray-900 */
  --accent: oklch(0.821 0.135 195.198); /* cyan-400 */
  --accent-foreground: oklch(0.208 0.042 265.755); /* gray-900 */

  /* Semantic Colors */
  --muted: oklch(0.984 0.003 247.858); /* gray-50 */
  --muted-foreground: oklch(0.446 0.043 257.281); /* gray-600 */
  --destructive: oklch(0.577 0.245 27.325); /* NOTE: Not defined in palette doc */
  --border: oklch(0.929 0.013 255.508); /* gray-200 */
  --input: oklch(0.984 0.003 247.858); /* gray-50 */
  --ring: oklch(0.608 0.279 245.831); /* blue-600 */

  /* Chart & Sidebar (Custom, keep themed) */
  --chart-1: oklch(0.608 0.279 245.831); /* blue-600 */
  --chart-2: oklch(0.714 0.123 238.75); /* sky-500 */
  --chart-3: oklch(0.821 0.135 195.198); /* cyan-400 */
  --chart-4: oklch(0.828 0.189 84.429);
  --chart-5: oklch(0.769 0.188 70.08);
  --sidebar: oklch(1 0 0);
  --sidebar-foreground: oklch(0.208 0.042 265.755);
  --sidebar-primary: oklch(0.608 0.279 245.831);
  --sidebar-primary-foreground: oklch(1 0 0);
  --sidebar-accent: oklch(0.984 0.003 247.858);
  --sidebar-accent-foreground: oklch(0.208 0.042 265.755);
  --sidebar-border: oklch(0.929 0.013 255.508);
  --sidebar-ring: oklch(0.608 0.279 245.831);
}

/* 
  Dark Theme Definitions
*/
.dark {
  /* Core Colors */
  --background: oklch(0.129 0.042 264.695); /* gray-950 */
  --foreground: oklch(0.984 0.003 247.858); /* gray-50 (Primary Text) */
  --card: oklch(0.208 0.042 265.755); /* gray-900 (Surface) */
  --card-foreground: oklch(0.984 0.003 247.858); /* gray-50 */
  --popover: oklch(0.208 0.042 265.755); /* gray-900 */
  --popover-foreground: oklch(0.984 0.003 247.858); /* gray-50 */

  /* Brand Colors */
  --primary: oklch(0.658 0.279 245.831); /* blue-500 */
  --primary-foreground: oklch(0.208 0.042 265.755); /* gray-900 */
  --secondary: oklch(0.764 0.123 238.75); /* sky-400 */
  --secondary-foreground: oklch(0.984 0.003 247.858); /* gray-50 */
  --accent: oklch(0.871 0.135 195.198); /* cyan-300 */
  --accent-foreground: oklch(0.208 0.042 265.755); /* gray-900 */

  /* Semantic Colors */
  --muted: oklch(0.279 0.041 260.031); /* gray-800 */
  --muted-foreground: oklch(0.704 0.04 256.788); /* gray-400 */
  --destructive: oklch(0.704 0.191 22.216); /* NOTE: Not defined in palette doc */
  --border: oklch(0.279 0.041 260.031); /* gray-800 */
  --input: oklch(0.208 0.042 265.755); /* gray-900 */
  --ring: oklch(0.658 0.279 245.831); /* blue-500 */

  /* Chart & Sidebar (Custom, keep themed) */
  --chart-1: oklch(0.714 0.123 238.75);
  --chart-2: oklch(0.821 0.135 195.198);
  --chart-3: oklch(0.608 0.279 245.831);
  --chart-4: oklch(0.627 0.265 303.9);
  --chart-5: oklch(0.645 0.246 16.439);
  --sidebar: oklch(0.258 0.035 256.765);
  --sidebar-foreground: oklch(0.984 0.003 247.858);
  --sidebar-primary: oklch(0.714 0.123 238.75);
  --sidebar-primary-foreground: oklch(0.208 0.042 265.755);
  --sidebar-accent: oklch(0.314 0.032 256.847);
  --sidebar-accent-foreground: oklch(0.984 0.003 247.858);
  --sidebar-border: oklch(0.314 0.032 256.847);
  --sidebar-ring: oklch(0.714 0.123 238.75);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  
  html {
    scroll-behavior: smooth;
  }
  
  body {
    @apply bg-background text-foreground;
    font-feature-settings: "rlig" 1, "calt" 1;
    text-rendering: optimizeLegibility;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }
  
  a, button {
    @apply cursor-pointer;
  }

  /* Focus styles for accessibility */
  *:focus-visible {
    outline: 2px solid var(--color-primary);
    outline-offset: 2px;
  }
}

@layer components {
  /* Modern card with glassmorphism effect */
  .card-modern {
    @apply bg-white/80 backdrop-blur-sm rounded-2xl border border-white/20 shadow-lg;
  }
  .dark .card-modern {
    @apply bg-gray-900/80 border-gray-700/30;
  }

  /* Footer specific component styles */
  .footer-container {
    @apply relative bg-gradient-to-br from-background via-muted/30 to-background;
  }
  .dark .footer-container {
    @apply from-background via-muted/20 to-card;
  }
  .footer-link {
    @apply relative text-muted-foreground transition-all duration-200 hover:text-foreground hover:translate-x-1;
  }
  .footer-link::after {
    content: '';
    @apply absolute -bottom-1 left-0 w-0 h-0.5 bg-gradient-to-r from-primary to-chart-2 transition-all duration-300;
  }
  .footer-link:hover::after {
    @apply w-full;
  }
  .footer-gradient-text {
    @apply bg-gradient-to-r from-primary to-secondary bg-clip-text text-transparent;
  }
  .footer-input {
    @apply px-4 py-3 bg-input border border-border rounded-lg text-foreground placeholder-muted-foreground backdrop-blur-sm focus:outline-none focus:ring-2 focus:ring-ring focus:border-transparent transition-all duration-200;
  }
  .footer-button-primary {
    @apply px-6 py-3 bg-gradient-to-r from-primary to-secondary text-primary-foreground rounded-lg font-medium shadow-lg hover:shadow-xl transition-all duration-200 hover:scale-105 transform-gpu;
  }
  .footer-button-secondary {
    @apply px-6 py-3 border-2 border-border text-muted-foreground rounded-lg font-medium hover:border-primary hover:text-foreground transition-all duration-200 hover:scale-102 transform-gpu;
  }
}

/* ============================================================================
CUSTOM UTILITIES
============================================================================ */

/* Performance & 3D Utilities */
@utility transform-gpu {
  transform: translate3d(0, 0, 0);
}

@utility backface-hidden {
  backface-visibility: hidden;
}

@utility perspective-1000 {
  perspective: 1000px;
}

@utility preserve-3d {
  transform-style: preserve-3d;
}

@utility will-change-transform {
  will-change: transform;
}

@utility will-change-auto {
  will-change: auto;
}

@utility animate-performance {
  will-change: transform, opacity;
  transform: translate3d(0, 0, 0);
}

@utility animate-performance-hover {
  &:hover {
    will-change: transform;
  }
}

/* Visual Effect Utilities */
@utility backdrop-blur-xxs {
  backdrop-filter: blur(0.2px);
}

@utility backdrop-blur-xx {
  backdrop-filter: blur(2.2px);
}

@utility backdrop-blur-footer {
  backdrop-filter: blur(8px) saturate(150%);
}

@utility text-shadow-glow {
  text-shadow: 0 0 10px currentColor;
}

@utility footer-gradient-bg {
  background: linear-gradient(135deg, hsl(var(--background)) 0%, hsl(var(--muted)) 100%);
}

/* Dark mode variant for footer-gradient-bg */
@custom-variant dark-footer-gradient (&:is(.dark *));

/* Scrollbar Styling */
@utility scrollbar-thin {
  scrollbar-width: thin;
  
  &::-webkit-scrollbar {
    width: 6px;
  }
  
  &::-webkit-scrollbar-track {
    background: transparent;
  }
  
  &::-webkit-scrollbar-thumb {
    background: rgba(0, 0, 0, 0.2);
    border-radius: 3px;
  }
  
  &::-webkit-scrollbar-thumb:hover {
    background: rgba(0, 0, 0, 0.3);
  }
}

/* Container Queries for Footer */
@utility footer-responsive {
  container-type: inline-size;
}

@utility footer-grid-md {
  @container (min-width: 768px) {
    grid-template-columns: repeat(2, 1fr);
  }
}

@utility footer-grid-lg {
  @container (min-width: 1024px) {
    grid-template-columns: repeat(4, 1fr);
  }
}

/* Accessibility-focused Footer Focus Ring */
@utility focus-ring-footer {
  &:focus-visible {
    outline: 2px solid hsl(var(--ring));
    outline-offset: 2px;
    border-radius: calc(var(--radius) - 2px);
  }
}

/* ============================================================================
GLOBAL MEDIA QUERIES FOR ACCESSIBILITY AND RESPONSIVENESS 
============================================================================ */

/* Dark mode scrollbar override */
@media (prefers-color-scheme: dark) {
  .scrollbar-thin::-webkit-scrollbar-thumb { 
    background: rgba(255, 255, 255, 0.2); 
  }
  .scrollbar-thin::-webkit-scrollbar-thumb:hover { 
    background: rgba(255, 255, 255, 0.3); 
  }
  
  .footer-gradient-bg {
    background: linear-gradient(135deg, hsl(var(--background)) 0%, hsl(var(--card)) 100%);
  }
}

/* Reduced motion preferences */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}

/* High contrast support */
@media (prefers-contrast: high) {
  .card-modern {
    border-width: 2px;
    border-color: currentColor;
  }
}

/* Print styles */
@media print {
  .no-print {
    display: none !important;
  }
  
  *,
  *::before,
  *::after {
    background: transparent !important;
    color: black !important;
    box-shadow: none !important;
    text-shadow: none !important;
  }
}