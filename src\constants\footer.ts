import type { Variants } from 'motion/react';
import type { FooterLegalLink } from '@/types';

/**
 * Container variants for footer animation
 */
export const containerVariants: Variants = {
  hidden: {
    opacity: 0,
  },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1,
      delayChildren: 0.2,
    },
  },
};

/**
 * Item variants for footer animation
 */
export const itemVariants: Variants = {
  hidden: {
    opacity: 0,
    y: 20,
  },
  visible: {
    opacity: 1,
    y: 0,
    transition: {
      duration: 0.6,
      ease: [0.25, 0.46, 0.45, 0.94] as const,
    },
  },
};

/**
 * Footer legal links configuration
 */
export const footerLegalLinks: FooterLegalLink[] = [
  {
    key: 'privacy',
    href: '/privacy-policy',
    external: false,
  },
  {
    key: 'terms',
    href: '/terms-of-service',
    external: false,
  },
  {
    key: 'cookies',
    href: '/cookies',
    external: false,
  },
];

/**
 * Helper function to get link attributes based on configuration
 */
export const getLinkAttributes = (link: FooterLegalLink) => {
  const attributes: Record<string, string> = {
    href: link.href,
  };

  if (link.external) {
    attributes.target = link.target || '_blank';
    attributes.rel = 'noopener noreferrer';
  }

  return attributes;
};
