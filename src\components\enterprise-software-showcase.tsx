'use client';

import Image from 'next/image';
import { motion } from 'motion/react';
import { useTranslation } from 'react-i18next';
import { softwareLogos } from '@/constants';

const EnterpriseSoftwareShowcase = () => {
  const { t } = useTranslation('common');

  return (
    <section className="relative min-h-screen overflow-hidden px-4 py-20">
      {/* Gradient Background */}
      <div className="from-background via-primary/5 to-accent/10 dark:from-background dark:via-primary/10 dark:to-accent/20 absolute inset-0 bg-gradient-to-br" />

      {/* Floating Background Elements */}
      <div className="absolute inset-0 overflow-hidden">
        {/* Animated geometric shapes */}
        <motion.div
          className="from-primary/20 to-secondary/20 absolute top-1/4 left-1/4 h-32 w-32 rounded-full bg-gradient-to-r blur-xl"
          animate={{
            x: [0, 100, 0],
            y: [0, -50, 0],
            scale: [1, 1.2, 1],
          }}
          transition={{
            duration: 8,
            repeat: Infinity,
            ease: 'easeInOut',
          }}
        />
        <motion.div
          className="from-accent/20 to-primary/20 absolute top-3/4 right-1/4 h-24 w-24 rounded-full bg-gradient-to-r blur-xl"
          animate={{
            x: [0, -80, 0],
            y: [0, 60, 0],
            scale: [1, 0.8, 1],
          }}
          transition={{
            duration: 10,
            repeat: Infinity,
            ease: 'easeInOut',
            delay: 2,
          }}
        />
        <motion.div
          className="from-secondary/30 to-accent/30 absolute top-1/2 right-1/3 h-16 w-16 rounded-full bg-gradient-to-r blur-lg"
          animate={{
            rotate: [0, 360],
            scale: [1, 1.5, 1],
          }}
          transition={{
            duration: 12,
            repeat: Infinity,
            ease: 'linear',
          }}
        />
      </div>

      {/* Main Content Container */}
      <div className="relative z-10 mx-auto max-w-7xl">
        {/* Hero Title */}
        <motion.div
          className="mb-16 text-center"
          initial={{ opacity: 0, y: 50 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, ease: 'easeOut' }}
        >
          <motion.h2
            className="from-primary via-secondary to-accent mb-6 bg-gradient-to-r bg-clip-text text-4xl leading-tight font-bold text-transparent md:text-6xl lg:text-7xl"
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 1, delay: 0.2 }}
          >
            {t('enterprise.title')}
          </motion.h2>
          <motion.div
            className="text-foreground relative text-5xl font-black md:text-7xl lg:text-8xl"
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.5 }}
          >
            <span className="relative z-10">{t('enterprise.subtitle')}</span>
            <motion.div
              className="from-primary/20 to-accent/20 absolute inset-0 -z-10 bg-gradient-to-r blur-2xl"
              animate={{
                scale: [1, 1.1, 1],
                opacity: [0.5, 0.8, 0.5],
              }}
              transition={{
                duration: 3,
                repeat: Infinity,
                ease: 'easeInOut',
              }}
            />
          </motion.div>
        </motion.div>

        {/* Software Logos Grid */}
        <motion.div
          className="grid grid-cols-2 items-center justify-items-center gap-8 md:grid-cols-3 md:gap-12 lg:grid-cols-6"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.8, delay: 0.8 }}
        >
          {softwareLogos.map((logo) => (
            <motion.div
              key={logo.name}
              className="group relative"
              initial={{
                opacity: 0,
                y: 50,
                rotateX: -15,
                rotateY: 15,
              }}
              animate={{
                opacity: 1,
                y: 0,
                rotateX: 0,
                rotateY: 0,
              }}
              transition={{
                duration: 0.8,
                delay: 1 + logo.delay,
                ease: 'easeOut',
              }}
              whileHover={{
                scale: 1.1,
                rotateY: 5,
                rotateX: 5,
                transition: { duration: 0.3 },
              }}
              style={{
                perspective: 1000,
                transformStyle: 'preserve-3d',
              }}
            >
              {/* 3D Card Effect */}
              <div className="group-hover:shadow-primary/20 relative rounded-2xl border border-white/20 bg-white/80 p-6 shadow-lg backdrop-blur-sm transition-all duration-300 group-hover:shadow-2xl">
                {/* Glow Effect */}
                <div className="from-primary/0 via-primary/10 to-accent/0 absolute inset-0 rounded-2xl bg-gradient-to-r opacity-0 transition-opacity duration-300 group-hover:opacity-100" />

                {/* Logo Image */}
                <div className="relative z-10 mx-auto h-20 w-20 md:h-24 md:w-24">
                  <Image
                    src={logo.src}
                    alt={logo.alt}
                    fill
                    className="object-contain filter transition-all duration-300 group-hover:brightness-110"
                    sizes="(max-width: 768px) 80px, 96px"
                  />
                </div>

                {/* Floating particles around logo */}
                <motion.div
                  className="bg-accent/60 absolute top-2 right-2 h-2 w-2 rounded-full"
                  animate={{
                    y: [0, -10, 0],
                    opacity: [0.6, 1, 0.6],
                  }}
                  transition={{
                    duration: 2,
                    repeat: Infinity,
                    delay: logo.delay,
                  }}
                />
                <motion.div
                  className="bg-secondary/60 absolute bottom-2 left-2 h-1.5 w-1.5 rounded-full"
                  animate={{
                    y: [0, 8, 0],
                    opacity: [0.4, 0.8, 0.4],
                  }}
                  transition={{
                    duration: 2.5,
                    repeat: Infinity,
                    delay: logo.delay + 0.5,
                  }}
                />
              </div>
            </motion.div>
          ))}

          {/* "And Others" Card */}
          <motion.div
            className="group relative"
            initial={{
              opacity: 0,
              y: 50,
              rotateX: -15,
              rotateY: 15,
            }}
            animate={{
              opacity: 1,
              y: 0,
              rotateX: 0,
              rotateY: 0,
            }}
            transition={{
              duration: 0.8,
              delay: 1.6,
              ease: 'easeOut',
            }}
            whileHover={{
              scale: 1.1,
              rotateY: -5,
              rotateX: 5,
              transition: { duration: 0.3 },
            }}
          >
            <div className="from-primary/10 to-accent/10 border-primary/20 group-hover:shadow-accent/20 relative rounded-2xl border bg-gradient-to-br p-6 shadow-lg backdrop-blur-sm transition-all duration-300 group-hover:shadow-2xl">
              <div className="mx-auto flex h-20 w-20 items-center justify-center md:h-24 md:w-24">
                <motion.span
                  className="text-muted-foreground text-center text-sm leading-tight font-semibold md:text-base"
                  animate={{
                    scale: [1, 1.05, 1],
                  }}
                  transition={{
                    duration: 2,
                    repeat: Infinity,
                    ease: 'easeInOut',
                  }}
                >
                  {t('enterprise.and_others')}
                </motion.span>
              </div>
            </div>
          </motion.div>
        </motion.div>
      </div>

      {/* Subtle Grid Pattern Overlay */}
      <div className="absolute inset-0 bg-[radial-gradient(circle_at_1px_1px,oklch(0.5_0.1_240)_1px,transparent_0)] [background-size:32px_32px] opacity-[0.03] dark:opacity-[0.05]" />
    </section>
  );
};

export default EnterpriseSoftwareShowcase;
