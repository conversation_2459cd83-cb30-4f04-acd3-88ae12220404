import React from 'react';
import type { Metadata } from 'next';
import { detectLanguageFromHeaders } from '@/lib/i18n/detector';
import { getServerTranslation } from '@/lib/i18n/server';

import HeroSection from '@/components/about/hero-section';
import CompanyStory from '@/components/about/company-story';
import ServicesGrid from '@/components/about/services-grid';
import TechnologyIntegrations from '@/components/about/technology-integrations';
import StatsSection from '@/components/about/stats-section';
import TeamSection from '@/components/about/team-section';
import ContactCTA from '@/components/about/contact-cta';

export async function generateMetadata(): Promise<Metadata> {
  const language = await detectLanguageFromHeaders();
  const { t } = await getServerTranslation(language, 'about');

  return {
    title: t('site.title'),
    description: t('site.description'),
    openGraph: {
      title: t('site.title'),
      description: t('site.description'),
      type: 'website',
    },
    twitter: {
      card: 'summary_large_image',
      title: t('site.title'),
      description: t('site.description'),
    },
  };
}

const AboutPage = () => {
  return (
    <>
      <HeroSection />
      <CompanyStory />
      <ServicesGrid />
      <TechnologyIntegrations />
      <StatsSection />
      <TeamSection />
      <ContactCTA />
    </>
  );
};

export default AboutPage;
