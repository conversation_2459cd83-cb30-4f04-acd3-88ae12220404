import React from 'react';
import Link from 'next/link';

type Props = {
  href: string;
  name: string;
};

const NavItem = ({ href, name }: Props) => {
  return (
    <Link
      href={href}
      className="group relative font-bold tracking-wide text-gray-700 transition-colors duration-200 ease-out hover:text-blue-600 dark:text-gray-300 dark:hover:text-blue-400"
    >
      {name}
      <span className="absolute -bottom-1 left-0 h-0.5 w-0 bg-blue-600 transition-all duration-200 ease-out group-hover:w-full dark:bg-blue-400"></span>
    </Link>
  );
};

export default NavItem;
