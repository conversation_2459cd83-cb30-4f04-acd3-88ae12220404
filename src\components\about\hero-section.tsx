'use client';

import React from 'react';
import { useTranslation, Trans } from 'react-i18next';
import { motion } from 'motion/react';
import { ArrowRight, Play } from 'lucide-react';
import { Button } from '@/components/ui/button';
import GradientHighlighter from '@/components/gradient-highlighter';

const HeroSection = () => {
  const { t } = useTranslation('about');

  return (
    <section className="from-background via-muted/30 to-background relative flex min-h-[80vh] items-center justify-center overflow-hidden bg-gradient-to-br px-4 py-20">
      {/* Background decorative elements */}
      <div className="pointer-events-none absolute inset-0">
        {/* Large gradient background orbs */}
        <motion.div
          initial={{ opacity: 0, scale: 0.8 }}
          animate={{ opacity: 0.3, scale: 1 }}
          transition={{ duration: 2 }}
          className="absolute -top-1/4 -left-1/4 h-1/2 w-1/2 rounded-full bg-gradient-to-br from-blue-500/40 to-cyan-500/30 blur-3xl dark:from-blue-500/20 dark:to-cyan-500/15"
        />
        <motion.div
          initial={{ opacity: 0, scale: 0.8 }}
          animate={{ opacity: 0.25, scale: 1 }}
          transition={{ duration: 2, delay: 0.5 }}
          className="absolute -right-1/4 -bottom-1/4 h-1/2 w-1/2 rounded-full bg-gradient-to-br from-cyan-500/30 to-blue-500/40 blur-3xl dark:from-cyan-500/15 dark:to-blue-500/20"
        />

        {/* Enhanced grid overlay */}
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 0.1 }}
          transition={{ duration: 2, delay: 1 }}
          className="absolute inset-0 dark:opacity-5"
          style={{
            backgroundImage:
              'linear-gradient(rgba(59, 130, 246, 0.1) 1px, transparent 1px), linear-gradient(90deg, rgba(59, 130, 246, 0.1) 1px, transparent 1px)',
            backgroundSize: '60px 60px',
          }}
        />
      </div>

      {/* Main content */}
      <div className="relative z-10 mx-auto max-w-4xl text-center">
        {/* Hero title */}
        <Trans
          i18nKey="hero.title"
          ns="about"
          parent={motion.h1}
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.2 }}
          className="mb-6 text-4xl leading-tight font-bold text-gray-900 text-shadow-2xs sm:text-5xl md:text-6xl lg:text-7xl dark:text-white"
          components={{
            highlighted: <GradientHighlighter />,
          }}
        />

        {/* Hero subtitle */}
        <motion.h2
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.4 }}
          className="text-primary mb-6 text-xl font-semibold text-shadow-xs sm:text-2xl md:text-3xl"
        >
          {t('hero.subtitle')}
        </motion.h2>

        {/* Hero description */}
        <motion.p
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.6 }}
          className="mx-auto mb-12 max-w-3xl text-lg leading-relaxed text-gray-700 sm:text-xl dark:text-gray-300"
        >
          {t('hero.description')}
        </motion.p>

        {/* CTA buttons */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.8 }}
          className="flex flex-col gap-4 sm:flex-row sm:justify-center"
          role="group"
          aria-label="Call to action buttons"
        >
          <Button
            size="lg"
            className="group from-primary to-secondary text-primary-foreground relative overflow-hidden bg-gradient-to-r shadow-lg transition-all duration-300 hover:scale-105 hover:shadow-xl focus:scale-105"
            aria-label={`${t('hero.cta.primary')} - Learn more about our company story`}
          >
            <span className="relative z-10 flex items-center gap-2">
              {t('hero.cta.primary')}
              <ArrowRight
                className="h-4 w-4 transition-transform group-hover:translate-x-1"
                aria-hidden="true"
              />
            </span>
            <div className="from-secondary to-primary absolute inset-0 bg-gradient-to-r opacity-0 transition-opacity duration-300 group-hover:opacity-100" />
          </Button>

          <Button
            variant="outline"
            size="lg"
            className="group border-primary/20 bg-background/80 hover:border-primary/40 hover:bg-primary/5 focus:border-primary/40 focus:bg-primary/5 border-2 backdrop-blur-sm transition-all duration-300"
            aria-label={`${t('hero.cta.secondary')} - Contact us for more information`}
          >
            <span className="flex items-center gap-2">
              <Play className="h-4 w-4" aria-hidden="true" />
              {t('hero.cta.secondary')}
            </span>
          </Button>
        </motion.div>
      </div>

      {/* Optimized floating elements with reduced motion for performance */}
      <div className="pointer-events-none absolute inset-0">
        {/* Floating geometric shapes with optimized animations */}
        <motion.div
          animate={{
            y: [0, -20, 0],
            rotate: [0, 5, 0],
          }}
          transition={{
            duration: 6,
            repeat: Infinity,
            ease: 'easeInOut',
            repeatType: 'reverse',
          }}
          className="absolute top-1/4 left-1/4 h-16 w-16 rounded-full bg-gradient-to-br from-blue-400/20 to-cyan-400/20 blur-sm will-change-transform"
          style={{ transform: 'translate3d(0, 0, 0)' }}
        />
        <motion.div
          animate={{
            y: [0, 15, 0],
            rotate: [0, -3, 0],
          }}
          transition={{
            duration: 8,
            repeat: Infinity,
            ease: 'easeInOut',
            delay: 1,
            repeatType: 'reverse',
          }}
          className="absolute top-3/4 right-1/4 h-12 w-12 rounded-full bg-gradient-to-br from-cyan-400/20 to-blue-400/20 blur-sm will-change-transform"
          style={{ transform: 'translate3d(0, 0, 0)' }}
        />
        <motion.div
          animate={{
            y: [0, -10, 0],
            x: [0, 5, 0],
          }}
          transition={{
            duration: 7,
            repeat: Infinity,
            ease: 'easeInOut',
            delay: 2,
            repeatType: 'reverse',
          }}
          className="from-primary/30 to-secondary/30 absolute top-1/2 right-1/6 h-8 w-8 rounded-full bg-gradient-to-br blur-sm will-change-transform"
          style={{ transform: 'translate3d(0, 0, 0)' }}
        />
      </div>
    </section>
  );
};

export default HeroSection;
