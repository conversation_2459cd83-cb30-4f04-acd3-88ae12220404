import { createInstance, type i18n } from 'i18next';
import { initReactI18next } from 'react-i18next/initReactI18next';
import resourcesToBackend from 'i18next-resources-to-backend';
import { getI18nextOptions, type Language, type Namespace } from './settings';
import debug from 'debug';

const langLog = debug('i18n:server:lang');

/**
 * Cache for server instances - key optimization for performance
 * Each combination of language+namespaces has its own instance
 */
const serverInstanceCache = new Map<string, i18n>();

/**
 * Creates or retrieves a cached i18next instance for server components.
 * According to the official documentation at i18next.com for server-side use.
 */
export async function getServerI18nInstance(
  lng: Language,
  namespaces: Namespace[] = ['common']
): Promise<i18n> {
  const cacheKey = `${lng}-${namespaces.join(',')}`;

  // Return the existing instance if it exists
  if (serverInstanceCache.has(cacheKey)) {
    return serverInstanceCache.get(cacheKey)!;
  }

  langLog(
    `🔧 Creating new server i18n instance for ${lng} with namespaces: ${namespaces.join(', ')}`
  );

  // Create a new instance according to the i18next documentation
  const instance = createInstance();

  await instance
    .use(initReactI18next)
    .use(
      resourcesToBackend((language: string, namespace: string) => {
        // Dynamic loading of translation files
        return import(`../../locales/${language}/${namespace}.json`).catch(
          (error) => {
            console.error(
              `Failed to load translation: ${language}/${namespace}.json`,
              error
            );
            return {}; // Fallback na prázdný objekt
          }
        );
      })
    )
    .init(getI18nextOptions(lng, namespaces));

  // Cache instance for future use
  serverInstanceCache.set(cacheKey, instance);

  return instance;
}

/**
 * Helper function for easy use in server components
 * Equivalent to the useTranslation hook for server-side
 */
export async function getServerTranslation(
  lng: Language,
  namespace: Namespace = 'common'
) {
  const i18nInstance = await getServerI18nInstance(lng, [namespace]);

  return {
    // Translation function similar to the client t() function
    t: (key: string, options?: Record<string, unknown>) => {
      return i18nInstance.t(key, { ...options, ns: namespace });
    },

    // Access to the entire instance for advanced use
    i18n: i18nInstance,

    // Formatting helper
    format: {
      number: (num: number) => new Intl.NumberFormat(lng).format(num),
      date: (date: Date) => new Intl.DateTimeFormat(lng).format(date),
      currency: (amount: number, currency = 'USD') =>
        new Intl.NumberFormat(lng, { style: 'currency', currency }).format(
          amount
        ),
    },
  };
}

/**
 * Batch loading for multiple namespaces at once
 * Optimization for pages that need multiple translation files
 */
export async function getServerTranslations(
  lng: Language,
  namespaces: Namespace[]
) {
  const i18nInstance = await getServerI18nInstance(lng, namespaces);

  // Create an object with translation functions for each namespace
  const translations: Record<
    string,
    (
      key: string,
      options?: Record<string, unknown>
    ) => ReturnType<typeof i18nInstance.t>
  > = {};

  for (const ns of namespaces) {
    translations[ns] = (key: string, options?: Record<string, unknown>) =>
      i18nInstance.t(key, { ...options, ns });
  }

  return {
    t: translations,
    i18n: i18nInstance,
  };
}
