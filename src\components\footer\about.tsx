import React from 'react';
import { motion } from 'motion/react';
import { useTranslation } from 'react-i18next';
import { itemVariants } from '@/constants/footer';
import {
  aboutSectionLinks,
  getSectionLinkAttributes,
} from '@/config/footer-sections';

const About = () => {
  const { t } = useTranslation('common');

  return (
    <motion.div variants={itemVariants}>
      <motion.h4
        className="footer-gradient-text mb-6 text-lg font-semibold"
        whileHover={{ scale: 1.02 }}
        transition={{ duration: 0.2 }}
      >
        {t('footer.sections.about.title')}
      </motion.h4>
      <motion.ul className="space-y-3 text-sm">
        {aboutSectionLinks.map((link) => {
          const linkAttributes = getSectionLinkAttributes(link);
          return (
            <motion.li key={link.key}>
              <motion.a
                {...linkAttributes}
                className="footer-link"
                whileHover={{ x: 4 }}
                whileTap={{ scale: 0.98 }}
                transition={{ duration: 0.2 }}
                aria-label={t(`footer.sections.about.links.${link.key}`)}
              >
                {t(`footer.sections.about.links.${link.key}`)}
              </motion.a>
            </motion.li>
          );
        })}
      </motion.ul>
    </motion.div>
  );
};

export default About;
