import React from 'react';
import { motion } from 'motion/react';
import { itemVariants } from '@/constants/footer';

const About = () => {
  return (
    <motion.div variants={itemVariants}>
      <motion.h4
        className="footer-gradient-text mb-6 text-lg font-semibold"
        whileHover={{ scale: 1.02 }}
        transition={{ duration: 0.2 }}
      >
        ABOUT
      </motion.h4>
      <motion.ul className="space-y-3 text-sm">
        {['About us', 'Contact', 'FAQ'].map((item, index) => (
          <motion.li key={index}>
            <motion.a
              href="#"
              className="footer-link"
              whileHover={{ x: 4 }}
              whileTap={{ scale: 0.98 }}
              transition={{ duration: 0.2 }}
            >
              {item}
            </motion.a>
          </motion.li>
        ))}
      </motion.ul>
    </motion.div>
  );
};

export default About;
