'use client';

import React from 'react';
import { motion } from 'motion/react';
import { containerVariants } from '@/constants/footer';

import Company from './company';
import Products from './products';
import About from './about';
import Newsletter from './newsletter';
import Copyright from './copyright';
import Decorative from './decorative';

const Footer = () => {
  return (
    <motion.footer
      className="footer-container text-foreground py-16 transition-all duration-700 ease-out"
      initial="hidden"
      whileInView="visible"
      viewport={{ once: true, margin: '-100px' }}
      variants={containerVariants}
    >
      <div className="container mx-auto max-w-7xl px-6">
        <motion.div
          className="grid gap-12 md:grid-cols-2 lg:grid-cols-4"
          variants={containerVariants}
        >
          {/* Company Section */}
          <Company />

          {/* Products Section */}
          <Products />

          {/* About Section */}
          <About />

          {/* Newsletter */}
          <Newsletter />
        </motion.div>

        {/* Copyright Section */}
        <Copyright />

        {/* Decorative gradient blur */}
        <Decorative />
      </div>
    </motion.footer>
  );
};

export default Footer;
