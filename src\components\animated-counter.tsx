'use client';
import React, { useRef, useState, useEffect } from 'react';
import { motion, useMotionValue, useSpring, useTransform } from 'motion/react';

type Props = {
  end: number;
  duration?: number;
  suffix?: string;
  prefix?: string;
};

const AnimatedCounter = ({
  end,
  duration = 2500,
  suffix = '',
  prefix = '',
}: Props) => {
  const [count, setCount] = useState(0);
  const [isVisible, setIsVisible] = useState(false);
  const countRef = useRef<HTMLSpanElement>(null);

  // Motion values for advanced animations
  const mouseX = useMotionValue(0);
  const mouseY = useMotionValue(0);
  const rotateX = useSpring(useTransform(mouseY, [-100, 100], [10, -10]), {
    stiffness: 100,
    damping: 30,
  });
  const rotateY = useSpring(useTransform(mouseX, [-100, 100], [-10, 10]), {
    stiffness: 100,
    damping: 30,
  });

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting && !isVisible) {
          setIsVisible(true);
          let startTime: number;
          const animate = (currentTime: number) => {
            if (!startTime) startTime = currentTime;
            const progress = Math.min((currentTime - startTime) / duration, 1);

            // Easing function for smooth animation
            const easeOutQuart = 1 - Math.pow(1 - progress, 4);
            setCount(Math.floor(easeOutQuart * end));

            if (progress < 1) {
              requestAnimationFrame(animate);
            }
          };
          requestAnimationFrame(animate);
        }
      },
      { threshold: 0.3 }
    );

    if (countRef.current) {
      observer.observe(countRef.current);
    }

    return () => observer.disconnect();
  }, [end, duration, isVisible]);

  const handleMouseMove = (event: React.MouseEvent<HTMLSpanElement>) => {
    const rect = event.currentTarget.getBoundingClientRect();
    const centerX = rect.left + rect.width / 2;
    const centerY = rect.top + rect.height / 2;
    mouseX.set(event.clientX - centerX);
    mouseY.set(event.clientY - centerY);
  };

  const handleMouseLeave = () => {
    mouseX.set(0);
    mouseY.set(0);
  };

  return (
    <motion.span
      ref={countRef}
      className="relative inline-block"
      style={{
        rotateX,
        rotateY,
        transformStyle: 'preserve-3d',
      }}
      onMouseMove={handleMouseMove}
      onMouseLeave={handleMouseLeave}
      initial={{ scale: 0, opacity: 0 }}
      animate={isVisible ? { scale: 1, opacity: 1 } : { scale: 0, opacity: 0 }}
      transition={{
        type: 'spring',
        stiffness: 100,
        damping: 15,
        delay: 0.2,
      }}
      whileHover={{
        scale: 1.05,
        textShadow: '0 0 20px rgba(37, 99, 235, 0.5)',
        transition: { duration: 0.3 },
      }}
    >
      <motion.span
        className="from-primary via-secondary to-accent bg-gradient-to-r bg-clip-text text-transparent drop-shadow-lg filter"
        initial={{ backgroundPosition: '0% 50%' }}
        animate={{ backgroundPosition: '100% 50%' }}
        transition={{
          duration: 3,
          repeat: Infinity,
          repeatType: 'reverse',
          ease: 'easeInOut',
        }}
        style={{
          backgroundSize: '200% 200%',
        }}
      >
        {prefix}
        {count.toLocaleString()}
        {suffix}
      </motion.span>

      {/* Glowing underline effect */}
      <motion.div
        className="from-primary to-secondary absolute -bottom-1 left-0 h-0.5 rounded-full bg-gradient-to-r"
        initial={{ width: 0, opacity: 0 }}
        animate={
          isVisible ? { width: '100%', opacity: 1 } : { width: 0, opacity: 0 }
        }
        transition={{ delay: 0.8, duration: 0.8, ease: 'easeOut' }}
      />
    </motion.span>
  );
};

export default AnimatedCounter;
