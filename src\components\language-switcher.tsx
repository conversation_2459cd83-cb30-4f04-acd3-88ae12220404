'use client';

import { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useRouter } from 'next/navigation';
import { changeClientLanguage } from '@/lib/i18n/client';
import { languageMetadata, type Language } from '@/lib/i18n/settings';
import { Globe, Check } from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Button } from '@/components/ui/button';
import FlagIcon from '@/components/flag-icon';
import debug from 'debug';

const langLog = debug('language-switcher:lang');

const LanguageSwitcher = () => {
  const { i18n } = useTranslation();
  const router = useRouter();
  const [isChanging, setIsChanging] = useState(false);
  const [mounted, setMounted] = useState(false);
  const currentLanguage = i18n.language as Language;

  useEffect(() => {
    setMounted(true);
  }, []);

  const handleLanguageChange = async (newLanguage: Language) => {
    if (newLanguage === currentLanguage || isChanging) {
      return;
    }

    setIsChanging(true);
    try {
      langLog(`🔄 Switching from ${currentLanguage} to ${newLanguage}`);
      // Change language in client i18next + sync with server
      await changeClientLanguage(newLanguage);
      // Refresh page for server components (without changing the URL!)
      router.refresh();
    } catch (error) {
      console.error('Language change failed:', error);
      // Fallback: full page reload
      window.location.reload();
    } finally {
      setIsChanging(false);
    }
  };

  // Prevent hydration mismatch
  if (!mounted) {
    return (
      <Button variant="ghost" size="sm" className="h-9 w-9 rounded-full">
        <div className="h-5 w-5" />
      </Button>
    );
  }

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          variant="ghost"
          size="sm"
          disabled={isChanging}
          className={`group relative h-9 w-9 overflow-hidden rounded-full border border-blue-200/50 bg-gradient-to-br from-blue-50 to-indigo-100 backdrop-blur-sm transition-all duration-300 ease-in-out hover:scale-105 hover:border-blue-300/60 hover:from-blue-100 hover:to-indigo-200 hover:shadow-lg hover:shadow-blue-500/25 dark:border-blue-800/50 dark:from-blue-900/20 dark:to-indigo-900/30 dark:hover:border-blue-700/60 dark:hover:from-blue-800/30 dark:hover:to-indigo-800/40 ${isChanging ? 'animate-pulse' : ''} `}
          aria-label="Change language"
        >
          {/* Background glow effect */}
          <div className="absolute inset-0 bg-gradient-to-br from-blue-400/20 to-indigo-600/20 opacity-0 transition-opacity duration-300 group-hover:opacity-100" />

          {/* Flag display with loading spinner overlay */}
          <div className="relative flex h-full w-full items-center justify-center">
            {isChanging ? (
              <Globe className="h-4 w-4 animate-spin text-blue-600 dark:text-blue-400" />
            ) : (
              <FlagIcon
                countryCode={currentLanguage}
                className="transition-all duration-300"
              />
            )}
          </div>
        </Button>
      </DropdownMenuTrigger>

      <DropdownMenuContent
        align="end"
        className="min-w-[160px] rounded-xl border border-gray-200/60 bg-white/95 p-1.5 shadow-xl shadow-gray-900/10 backdrop-blur-md dark:border-gray-800/60 dark:bg-gray-900/95"
        sideOffset={8}
      >
        {Object.entries(languageMetadata).map(([langCode, meta]) => {
          const isActive = langCode === currentLanguage;
          return (
            <DropdownMenuItem
              key={langCode}
              onClick={() => handleLanguageChange(langCode as Language)}
              disabled={isActive || isChanging}
              className={`flex cursor-pointer items-center justify-between rounded-lg px-3 py-3 transition-all duration-200 ease-in-out hover:bg-gradient-to-r hover:from-blue-50 hover:to-indigo-50 focus:bg-gradient-to-r focus:from-blue-50 focus:to-indigo-50 dark:hover:from-blue-900/30 dark:hover:to-indigo-900/30 dark:focus:from-blue-900/30 dark:focus:to-indigo-900/30 ${
                isActive
                  ? 'bg-gradient-to-r from-blue-100 to-indigo-100 shadow-sm dark:from-blue-900/40 dark:to-indigo-900/40'
                  : ''
              } ${isActive || isChanging ? 'cursor-default' : 'hover:scale-[1.02]'} `}
            >
              {/* Flag and label */}
              <div className="flex items-center justify-start gap-3">
                <div className="flex h-6 w-6 items-center justify-center">
                  <FlagIcon
                    countryCode={langCode}
                    className={`transition-all duration-200 ${isActive ? 'scale-110' : 'scale-100'}`}
                  />
                </div>
                <span className="text-sm leading-none font-medium text-gray-700 dark:text-gray-300">
                  {meta.nativeName}
                </span>
              </div>

              {/* Active indicator */}
              {isActive && (
                <div className="flex h-5 w-5 items-center justify-center">
                  <Check className="animate-in fade-in h-4 w-4 text-blue-600 duration-200 dark:text-blue-400" />
                </div>
              )}
            </DropdownMenuItem>
          );
        })}
      </DropdownMenuContent>
    </DropdownMenu>
  );
};

export default LanguageSwitcher;
