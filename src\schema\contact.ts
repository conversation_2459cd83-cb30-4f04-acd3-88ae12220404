import { z } from 'zod';

export const createContactSchema = (t: (key: string) => string) => {
  return z.object({
    firstName: z
      .string()
      .min(1, t('form.validation.firstName.required'))
      .min(2, t('form.validation.firstName.min')),
    lastName: z
      .string()
      .min(1, t('form.validation.lastName.required'))
      .min(2, t('form.validation.lastName.min')),
    email: z
      .string()
      .min(1, t('form.validation.email.required'))
      .email(t('form.validation.email.invalid')),
    phone: z
      .string()
      .min(1, t('form.validation.phone.required'))
      .regex(/^[+]?[\d\s\-()]+$/, t('form.validation.phone.invalid')),
    company: z.string().optional(),
    message: z
      .string()
      .min(1, t('form.validation.message.required'))
      .min(10, t('form.validation.message.min')),
    consent: z
      .boolean()
      .refine((val) => val === true, t('form.validation.consent.required')),
  });
};

export type ContactFormData = z.infer<ReturnType<typeof createContactSchema>>;
