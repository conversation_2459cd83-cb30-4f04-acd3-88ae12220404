import React from 'react';
import { motion } from 'motion/react';
import { itemVariants } from '@/constants/footer';

const Newsletter = () => {
  return (
    <motion.div className="space-y-6" variants={itemVariants}>
      <motion.h4
        className="footer-gradient-text text-lg font-semibold"
        whileHover={{ scale: 1.02 }}
        transition={{ duration: 0.2 }}
      >
        Stay Updated
      </motion.h4>

      <motion.p className="text-muted-foreground text-sm leading-relaxed">
        Get the latest updates on document processing innovations and Centris
        features.
      </motion.p>

      <motion.div className="space-y-4">
        <motion.div
          className="flex flex-col gap-3 sm:flex-row"
          whileHover={{ scale: 1.02 }}
          transition={{ duration: 0.2 }}
        >
          <motion.input
            type="email"
            placeholder="Enter your email"
            className="footer-input flex-1"
            whileFocus={{
              scale: 1.02,
              transition: { duration: 0.2 },
            }}
          />
          <motion.button
            className="footer-button-primary"
            whileHover={{
              scale: 1.05,
              transition: { duration: 0.2 },
            }}
            whileTap={{ scale: 0.98 }}
          >
            Subscribe
          </motion.button>
        </motion.div>

        <motion.button
          className="footer-button-secondary w-full"
          whileHover={{
            scale: 1.02,
            transition: { duration: 0.2 },
          }}
          whileTap={{ scale: 0.98 }}
        >
          Contact Us
        </motion.button>
      </motion.div>
    </motion.div>
  );
};

export default Newsletter;
