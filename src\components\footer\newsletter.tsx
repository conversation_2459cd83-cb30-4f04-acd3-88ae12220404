'use client';

import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { motion } from 'motion/react';
import { toast } from 'sonner';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import {
  createNewsletterSchema,
  NewsletterFormData,
} from '@/schema/newsletter';
import { Mail, Loader2, MessageCircle } from 'lucide-react';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { itemVariants } from '@/constants/footer';

const Newsletter = () => {
  const { t } = useTranslation('common');
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Create schema with current translations
  const newsletterSchema = createNewsletterSchema(t);

  const form = useForm<NewsletterFormData>({
    resolver: zodResolver(newsletterSchema),
    defaultValues: {
      email: '',
    },
  });

  const onSubmit = async (data: NewsletterFormData) => {
    setIsSubmitting(true);

    try {
      // Simulate API call
      await new Promise((resolve) => {
        console.log('Newsletter subscription:', data);
        setTimeout(resolve, 2000);
      });

      // Show success toast
      toast.success(t('footer.newsletter.form.success.title'), {
        description: t('footer.newsletter.form.success.description'),
      });

      // Reset form
      form.reset();
    } catch (error) {
      // Show error toast
      toast.error(t('footer.newsletter.form.error.title'), {
        description: t('footer.newsletter.form.error.description'),
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleContactClick = () => {
    // Navigate to contact page or scroll to contact section
    window.location.href = '/contact';
  };

  return (
    <motion.div className="space-y-6" variants={itemVariants}>
      <motion.h4
        className="footer-gradient-text text-lg font-semibold"
        whileHover={{ scale: 1.02 }}
        transition={{ duration: 0.2 }}
      >
        {t('footer.newsletter.title')}
      </motion.h4>

      <motion.p className="text-muted-foreground text-sm leading-relaxed">
        {t('footer.newsletter.description')}
      </motion.p>

      <motion.div className="space-y-4">
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)}>
            <motion.div
              className="flex flex-col gap-3 sm:flex-row"
              whileHover={{ scale: 1.02 }}
              transition={{ duration: 0.2 }}
            >
              <FormField
                control={form.control}
                name="email"
                render={({ field }) => (
                  <FormItem className="flex-1">
                    <FormControl>
                      <motion.div
                        whileFocus={{
                          scale: 1.02,
                          transition: { duration: 0.2 },
                        }}
                      >
                        <Input
                          type="email"
                          placeholder={t(
                            'footer.newsletter.form.email.placeholder'
                          )}
                          className="footer-input"
                          {...field}
                        />
                      </motion.div>
                    </FormControl>
                    <FormMessage className="text-xs" />
                  </FormItem>
                )}
              />
              <motion.div
                whileHover={{
                  scale: 1.05,
                  transition: { duration: 0.2 },
                }}
                whileTap={{ scale: 0.98 }}
              >
                <Button
                  type="submit"
                  disabled={isSubmitting}
                  className="footer-button-primary whitespace-nowrap"
                >
                  {isSubmitting ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      {t('footer.newsletter.form.subscribing')}
                    </>
                  ) : (
                    <>
                      <Mail className="mr-2 h-4 w-4" />
                      {t('footer.newsletter.form.subscribe')}
                    </>
                  )}
                </Button>
              </motion.div>
            </motion.div>
          </form>
        </Form>

        <motion.div
          whileHover={{
            scale: 1.02,
            transition: { duration: 0.2 },
          }}
          whileTap={{ scale: 0.98 }}
        >
          <Button
            onClick={handleContactClick}
            variant="outline"
            className="footer-button-secondary w-full"
          >
            <MessageCircle className="mr-2 h-4 w-4" />
            {t('footer.newsletter.form.contact')}
          </Button>
        </motion.div>
      </motion.div>
    </motion.div>
  );
};

export default Newsletter;
