import React from 'react';
import { motion } from 'motion/react';
import { itemVariants } from '@/constants/footer';

const Products = () => {
  return (
    <motion.div variants={itemVariants}>
      <motion.h4
        className="footer-gradient-text mb-6 text-lg font-semibold"
        whileHover={{ scale: 1.02 }}
        transition={{ duration: 0.2 }}
      >
        PRODUCTS
      </motion.h4>
      <motion.ul className="space-y-3 text-sm">
        {[
          'Centris mailroom',
          'Centris received invoices',
          'Centris issued invoices',
          'Centris contracts',
          'Centris requisitions',
          'Centris directives',
          'Digital archive',
          'Identity management',
        ].map((item, index) => (
          <motion.li key={index}>
            <motion.a
              href="#"
              className="footer-link"
              whileHover={{ x: 4 }}
              whileTap={{ scale: 0.98 }}
              transition={{ duration: 0.2 }}
            >
              {item}
            </motion.a>
          </motion.li>
        ))}
      </motion.ul>
    </motion.div>
  );
};

export default Products;
