'use client';

import React from 'react';
import { useTranslation, Trans } from 'react-i18next';
import { motion } from 'motion/react';
import { ArrowRight, Calendar, Play, CheckCircle } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import GradientHighlighter from '@/components/gradient-highlighter';

const ContactCTA = () => {
  const { t } = useTranslation('about');

  return (
    <section className="from-muted/30 to-background bg-gradient-to-b px-4 py-20">
      <div className="mx-auto max-w-7xl">
        {/* Main CTA card */}
        <motion.div
          initial={{ opacity: 0, y: 50 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true, margin: '-100px' }}
          transition={{ duration: 0.8 }}
          className="relative overflow-hidden"
        >
          <Card className="border-primary/20 from-primary/5 via-background to-secondary/5 overflow-hidden bg-gradient-to-br">
            {/* Background decorative elements */}
            <div className="pointer-events-none absolute inset-0">
              {/* Gradient orbs */}
              <div className="from-primary/20 to-secondary/20 absolute -top-1/4 -right-1/4 h-1/2 w-1/2 rounded-full bg-gradient-to-br blur-3xl" />
              <div className="from-secondary/20 to-primary/20 absolute -bottom-1/4 -left-1/4 h-1/2 w-1/2 rounded-full bg-gradient-to-br blur-3xl" />

              {/* Grid pattern */}
              <div
                className="absolute inset-0 opacity-5"
                style={{
                  backgroundImage:
                    'linear-gradient(rgba(59, 130, 246, 0.1) 1px, transparent 1px), linear-gradient(90deg, rgba(59, 130, 246, 0.1) 1px, transparent 1px)',
                  backgroundSize: '40px 40px',
                }}
              />
            </div>

            <CardContent className="relative z-10 p-8 text-center md:p-12">
              {/* Header */}
              <Trans
                i18nKey="contact.title"
                ns="about"
                parent={motion.h2}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.8 }}
                className="mb-4 text-3xl font-bold text-gray-900 sm:text-4xl md:text-5xl dark:text-white"
                components={{
                  highlighted: <GradientHighlighter />,
                }}
              />

              <motion.p
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.8, delay: 0.2 }}
                className="text-primary mb-4 text-xl font-semibold"
              >
                {t('contact.subtitle')}
              </motion.p>

              <motion.p
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.8, delay: 0.4 }}
                className="mx-auto mb-12 max-w-3xl text-lg text-gray-700 dark:text-gray-300"
              >
                {t('contact.description')}
              </motion.p>

              {/* CTA buttons */}
              <motion.div
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.8, delay: 0.6 }}
                className="mb-12 flex flex-col justify-center gap-4 sm:flex-row"
              >
                <Button
                  size="lg"
                  className="group from-primary to-secondary text-primary-foreground relative overflow-hidden bg-gradient-to-r shadow-lg transition-all duration-300 hover:scale-105 hover:shadow-xl"
                >
                  <span className="relative z-10 flex items-center gap-2">
                    <Calendar className="h-5 w-5" />
                    {t('contact.cta.primary')}
                    <ArrowRight className="h-4 w-4 transition-transform group-hover:translate-x-1" />
                  </span>
                  <div className="from-secondary to-primary absolute inset-0 bg-gradient-to-r opacity-0 transition-opacity duration-300 group-hover:opacity-100" />
                </Button>

                <Button
                  variant="outline"
                  size="lg"
                  className="group border-primary/20 bg-background/80 hover:border-primary/40 hover:bg-primary/5 border-2 backdrop-blur-sm transition-all duration-300"
                >
                  <span className="flex items-center gap-2">
                    <Play className="h-4 w-4" />
                    {t('contact.cta.secondary')}
                  </span>
                </Button>
              </motion.div>

              {/* Benefits list */}
              <motion.div
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.8, delay: 0.8 }}
                className="mx-auto grid max-w-4xl gap-4 sm:grid-cols-2 lg:grid-cols-4"
              >
                {(() => {
                  const benefits = t('contact.benefits', {
                    returnObjects: true,
                  });
                  const benefitsArray = Array.isArray(benefits) ? benefits : [];
                  return benefitsArray.map((benefit, index) => (
                    <motion.div
                      key={index}
                      initial={{ opacity: 0, y: 20 }}
                      whileInView={{ opacity: 1, y: 0 }}
                      viewport={{ once: true }}
                      transition={{ duration: 0.5, delay: 0.8 + index * 0.1 }}
                      className="bg-background/50 border-border/50 flex items-start gap-3 rounded-lg border p-4 backdrop-blur-sm"
                    >
                      <div className="from-primary to-secondary mt-0.5 flex h-6 w-6 flex-shrink-0 items-center justify-center rounded-full bg-gradient-to-r">
                        <CheckCircle className="h-4 w-4 text-white" />
                      </div>
                      <span className="text-sm leading-relaxed text-gray-700 dark:text-gray-300">
                        {benefit}
                      </span>
                    </motion.div>
                  ));
                })()}
              </motion.div>
            </CardContent>
          </Card>
        </motion.div>

        {/* Additional contact information */}
        <motion.div
          initial={{ opacity: 0, y: 50 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true, margin: '-100px' }}
          transition={{ duration: 0.8, delay: 0.4 }}
          className="mt-12 grid gap-6 md:grid-cols-3"
        >
          {/* Quick Response */}
          <Card className="border-border/50 bg-card/80 backdrop-blur-sm transition-all duration-300 hover:shadow-lg">
            <CardContent className="p-6 text-center">
              <div className="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-blue-100 dark:bg-blue-900/30">
                <ArrowRight className="h-6 w-6 text-blue-600 dark:text-blue-400" />
              </div>
              <h3 className="mb-2 text-lg font-semibold text-gray-900 dark:text-white">
                Quick Response
              </h3>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                Get a response within 24 hours from our expert team
              </p>
            </CardContent>
          </Card>

          {/* Personalized Demo */}
          <Card className="border-border/50 bg-card/80 backdrop-blur-sm transition-all duration-300 hover:shadow-lg">
            <CardContent className="p-6 text-center">
              <div className="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-green-100 dark:bg-green-900/30">
                <Play className="h-6 w-6 text-green-600 dark:text-green-400" />
              </div>
              <h3 className="mb-2 text-lg font-semibold text-gray-900 dark:text-white">
                Personalized Demo
              </h3>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                See Centris in action with a demo tailored to your needs
              </p>
            </CardContent>
          </Card>

          {/* Expert Consultation */}
          <Card className="border-border/50 bg-card/80 backdrop-blur-sm transition-all duration-300 hover:shadow-lg">
            <CardContent className="p-6 text-center">
              <div className="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-purple-100 dark:bg-purple-900/30">
                <CheckCircle className="h-6 w-6 text-purple-600 dark:text-purple-400" />
              </div>
              <h3 className="mb-2 text-lg font-semibold text-gray-900 dark:text-white">
                Expert Consultation
              </h3>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                Free consultation with our document processing specialists
              </p>
            </CardContent>
          </Card>
        </motion.div>

        {/* Final encouragement */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true, margin: '-100px' }}
          transition={{ duration: 0.8, delay: 0.6 }}
          className="mt-12 text-center"
        >
          <p className="mx-auto max-w-2xl text-gray-600 dark:text-gray-400">
            Join hundreds of satisfied customers who have transformed their
            document processing with Centris. Take the first step towards
            operational excellence today.
          </p>
        </motion.div>
      </div>
    </section>
  );
};

export default ContactCTA;
