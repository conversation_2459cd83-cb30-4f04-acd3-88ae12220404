'use client';

import { motion } from 'motion/react';

const BackgroundLetter = ({
  letter,
  position,
  delay,
  size = 'text-3xl',
  color = 'text-gray-600',
}: {
  letter: string;
  position: string;
  delay: number;
  size?: string;
  color?: string;
}) => (
  <motion.div
    className={`absolute ${position} ${size} ${color} pointer-events-none font-extrabold opacity-40 select-none dark:text-gray-400 dark:opacity-30`}
    initial={{ opacity: 0, scale: 0.5, rotate: -90 }}
    animate={{
      opacity: [0, 0.4, 0.2, 0.4],
      scale: [0.5, 1.2, 0.8, 1],
      rotate: [0, 15, -10, 5, 0],
    }}
    transition={{
      duration: 12,
      delay,
      repeat: Infinity,
      ease: 'easeInOut',
    }}
  >
    {letter}
  </motion.div>
);

export default BackgroundLetter;
