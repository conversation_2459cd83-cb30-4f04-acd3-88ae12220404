# Centris


Centris, the leading-edge electronic document processing system, transforms the way you manage and organize all kinds of your documents. Say goodbye to manual processing and data extraction, and embrace the future with <PERSON><PERSON><PERSON>!

## Getting Started

First, run the development server:

```bash
npm run dev
```

Open [http://localhost:3000](http://localhost:3000) with your browser to see the result.

## Node.js Version Management

This project uses `.nvmrc` to specify the required Node.js version.

### Usage

```bash
# Install the specified Node.js version
nvm install

# Use the version defined in .nvmrc
nvm use

# For Windows (if automatic detection doesn't work):
nvm use $(cat .nvmrc)

or 

nvm use 22.16.0
```

### Verify version
```bash
node -v
# Should output: v22.16.0
```

## Commit Message Conventions

This project follows the **Conventional Commits** specification for consistent and meaningful commit messages. Please use the following prefixes:

- `feat:` - A new feature for the user
- `fix:` - A bug fix
- `chore:` - Maintenance tasks (dependency updates, build process changes, etc.)
- `docs:` - Documentation changes
- `style:` - Code formatting, whitespace, missing semi-colons (no production code change)
- `refactor:` - Code refactoring without changing functionality
- `test:` - Adding or updating tests
- `perf:` - Performance improvements
- `ci:` - Continuous Integration related changes

### Examples

```
feat: add user authentication system
fix: resolve login redirect issue
chore: update package dependencies
docs: update API documentation
style: format code with prettier
refactor: simplify user validation logic
```

### Format

```
<type>: <description>

[optional body]

[optional footer]
```

For more information, see [Conventional Commits](https://www.conventionalcommits.org/).

# Debugging

1. See All Debug Messages
Use the wildcard * to enable all debug output from the application. This is the most common way to start.

DEBUG=*

2. See Messages from a Specific Part
If you only want to see logs from a specific part of the app (e.g., api:users), specify its namespace.

DEBUG=api:users

3. See Messages from a Whole Section
Use a wildcard to see all logs that start with a certain prefix (e.g., everything related to the API).

DEBUG=api:*

4. See Multiple Parts
Separate multiple namespaces with a comma (without spaces).

DEBUG=api:*,db:query

5. Exclude a Noisy Part
Enable everything but exclude a specific namespace by prefixing it with a -. This is useful for hiding logs you don't need.

DEBUG=*,-db:*

# shadcn/ui Installation

## ⚠️ Important Note for Next.js 15 + React 19 Setup

Due to peer dependency conflicts between **Next.js 15**, **React 19**, and **ESLint 9**, shadcn/ui requires the `--legacy-peer-deps` flag during installation.

### Initial Setup

```bash
npx --legacy-peer-deps shadcn@latest init
```

### Alternative Method

If the above doesn't work, manually install dependencies first:

```bash
npm install tailwindcss-animate class-variance-authority clsx tailwind-merge lucide-react --legacy-peer-deps
npx shadcn@latest init
```

### Adding Components

When adding individual shadcn/ui components, you'll also need to use the flag:

```bash
npx --legacy-peer-deps shadcn@latest add button
npx --legacy-peer-deps shadcn@latest add card
npx --legacy-peer-deps shadcn@latest add dialog
```

### Why This Is Needed

This is a known compatibility issue with:
- Next.js 15.x
- React 19.x 
- ESLint 9.x
- TypeScript ESLint v7/v8 conflicts

The shadcn/ui team is actively working on full React 19 compatibility. This workaround is the officially recommended solution until all dependencies are updated.

### Testing

Always test your application thoroughly after installing new components, as `--legacy-peer-deps` bypasses certain compatibility checks.

---

For more information, see the [official shadcn/ui React 19 compatibility guide](https://ui.shadcn.com/docs/react-19).

# Translation System Documentation

This section provides comprehensive guidance on implementing and using our i18next-based translation system in Next.js 15. Our approach prioritizes clean URLs without language prefixes while maintaining full internationalization capabilities through intelligent language detection and server-client synchronization.

## Understanding Our Translation Architecture

Our translation system operates on a fundamental principle that separates content from structure. Think of it as having a smart library where the same book exists in multiple languages, but you always access it through the same catalog number. When you request `/about`, the system automatically serves the content in your preferred language without changing the URL structure.

The architecture consists of three primary layers that work together seamlessly. The detection layer identifies user language preferences through cookies and browser headers. The server layer provides translations for initial page renders and static content. The client layer handles interactive elements and dynamic language switching. This separation ensures optimal performance while maintaining a consistent user experience across all interaction patterns.

## Translation File Organization

Our translation files follow a structured hierarchy designed to promote maintainability and developer productivity. The organization reflects both functional boundaries and content relationships, making it intuitive for developers to locate and manage translation content.

### Directory Structure Convention

```
src/locales/
├── en/                          # English translations (primary language)
│   ├── common.json             # Shared translations across all pages
│   ├── navigation.json         # Navigation elements and menu items
│   ├── forms.json             # Form labels, validation messages
│   ├── errors.json            # Error messages and status notifications
│   ├── homepage.json          # Homepage-specific content
│   ├── about.json             # About page content
│   └── products/              # Product-related translations
│       ├── catalog.json       # Product catalog content
│       └── details.json       # Product detail page content
└── cs/                         # Czech translations (mirror structure)
    ├── common.json
    ├── navigation.json
    ├── forms.json
    ├── errors.json
    ├── homepage.json
    ├── about.json
    └── products/
        ├── catalog.json
        └── details.json
```

### Namespace Naming Conventions

Each JSON file represents a namespace in our i18next configuration. The naming convention follows these principles to ensure clarity and prevent conflicts. File names should be descriptive and reflect their content scope rather than their location in the application. For instance, `forms.json` contains all form-related translations regardless of which pages use forms, while `homepage.json` contains content specific to the homepage experience.

When creating new namespaces, consider the content's lifecycle and reusability. Shared content that appears across multiple pages belongs in `common.json`, while page-specific content deserves its own namespace. This approach prevents translation key conflicts and makes it easier for developers to locate relevant translations when building new features.

### Translation Key Structure

Within each JSON file, we follow a hierarchical key structure that mirrors the logical organization of content. This approach makes translations self-documenting and reduces the cognitive load on developers when searching for specific translations.

```json
{
  "sections": {
    "hero": {
      "title": "Welcome to Our Platform",
      "subtitle": "Experience the future of digital innovation",
      "cta": {
        "primary": "Get Started",
        "secondary": "Learn More"
      }
    },
    "features": {
      "quality": {
        "title": "Premium Quality",
        "description": "We deliver excellence in every detail"
      }
    }
  },
  "meta": {
    "title": "Homepage - Your Company",
    "description": "Discover our innovative solutions"
  }
}
```

The key structure reflects content hierarchy rather than DOM structure, which makes translations more resilient to design changes. When the design team reorganizes page elements, the translation keys remain stable and meaningful.

## Server Component Translation Usage

Server components represent the foundation of our translation system, providing the initial translated content that users see when they first visit any page. These components execute on the server during each request, allowing them to access the complete translation system and deliver fully localized content in the initial HTML response.

### Basic Server Component Implementation

Server components utilize our `getServerTranslation` function to access translations during the server-side rendering process. This function creates an optimized i18next instance specifically for server-side use, ensuring that translations are available immediately without additional client-side requests.

```typescript
import { detectLanguageFromHeaders } from '@/lib/i18n/detector';
import { getServerTranslation } from '@/lib/i18n/server';

/**
 * Server component example demonstrating basic translation usage
 * This component renders during server-side processing and delivers
 * translated content in the initial HTML response
 */
export default async function AboutPage() {
  // Detect the user's preferred language from browser headers and cookies
  // This detection happens automatically through our middleware system
  const language = await detectLanguageFromHeaders();
  
  // Load translations for the specific namespace this component needs
  // The 'about' namespace contains all content specific to the about page
  const { t, format } = await getServerTranslation(language, 'about');
  
  // Additional namespace can be loaded if the component needs shared content
  const { t: commonT } = await getServerTranslation(language, 'common');

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Page title using about-specific translations */}
      <h1 className="text-4xl font-bold mb-6">
        {t('sections.hero.title')}
      </h1>
      
      {/* Subtitle with proper typography hierarchy */}
      <p className="text-xl text-gray-600 mb-8">
        {t('sections.hero.subtitle')}
      </p>
      
      {/* Mission statement section */}
      <section className="mb-12">
        <h2 className="text-2xl font-semibold mb-4">
          {t('sections.mission.title')}
        </h2>
        <p className="text-gray-700 leading-relaxed">
          {t('sections.mission.description')}
        </p>
      </section>
      
      {/* Contact information using common translations */}
      <section>
        <h2 className="text-2xl font-semibold mb-4">
          {commonT('contact.title')}
        </h2>
        <p className="text-gray-700">
          {commonT('contact.email')}: <EMAIL>
        </p>
      </section>
    </div>
  );
}
```

### Advanced Server Component Patterns

For more complex server components that require multiple namespaces or dynamic content generation, our system provides additional utilities that streamline the translation loading process while maintaining optimal performance characteristics.

```typescript
import { detectLanguageFromHeaders } from '@/lib/i18n/detector';
import { getServerTranslations } from '@/lib/i18n/server';

/**
 * Advanced server component demonstrating multi-namespace usage
 * This pattern is ideal for complex pages that combine multiple content types
 */
export default async function ProductCatalogPage() {
  const language = await detectLanguageFromHeaders();
  
  // Load multiple namespaces efficiently in a single operation
  // This approach reduces the number of i18next instances created
  const { t } = await getServerTranslations(language, [
    'common',
    'navigation', 
    'products/catalog',
    'forms'
  ]);

  // Simulate product data that would typically come from a database
  const products = [
    { id: 1, category: 'premium', inStock: true },
    { id: 2, category: 'standard', inStock: false },
  ];

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Navigation breadcrumbs using navigation namespace */}
      <nav className="mb-6">
        <span>{t.navigation('breadcrumbs.home')}</span>
        <span className="mx-2">/</span>
        <span>{t.navigation('breadcrumbs.products')}</span>
      </nav>
      
      {/* Page header using catalog-specific translations */}
      <header className="mb-8">
        <h1 className="text-4xl font-bold">
          {t['products/catalog']('sections.header.title')}
        </h1>
        <p className="text-gray-600 mt-2">
          {t['products/catalog']('sections.header.subtitle')}
        </p>
      </header>
      
      {/* Product grid with dynamic translations */}
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
        {products.map((product) => (
          <div key={product.id} className="border rounded-lg p-4">
            <h3 className="font-semibold">
              {t['products/catalog'](`items.${product.category}.name`)}
            </h3>
            <p className="text-gray-600">
              {t['products/catalog'](`items.${product.category}.description`)}
            </p>
            
            {/* Stock status with conditional translations */}
            <div className="mt-4">
              <span className={`px-2 py-1 rounded text-sm ${
                product.inStock ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
              }`}>
                {product.inStock 
                  ? t.common('status.in_stock')
                  : t.common('status.out_of_stock')
                }
              </span>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}
```

### When to Use Server Components for Translations

Server components provide the optimal translation experience for content that remains static during the user's page visit. This includes page headers, main content areas, navigation elements, and any text that does not change based on user interactions. The primary advantage lies in delivering fully translated content in the initial HTML response, which improves both performance and search engine optimization.

Choose server components when you need translations for content that appears above the fold, contributes to the page's SEO value, or represents the core message of the page. Server-side translation ensures that search engines can properly index your multilingual content and that users see translated content immediately upon page load.

## Client Component Translation Usage

Client components handle the interactive aspects of our translation system, enabling real-time language switching and dynamic content updates without requiring page refreshes. These components execute in the browser and use React hooks to access the translation system, providing immediate responsiveness to user language preferences.

### Basic Client Component Implementation

Client components utilize the `useTranslation` hook from react-i18next to access translations dynamically. This hook provides immediate access to the translation function and language information, enabling components to react to language changes in real-time.

```typescript
'use client';

import { useTranslation } from 'react-i18next';
import { useState } from 'react';
import { motion } from 'motion/react';
import { Star, Heart, Share } from 'lucide-react';

/**
 * Client component example demonstrating interactive translation usage
 * This component provides real-time language switching and dynamic content updates
 */
export default function InteractiveProductCard() {
  // Access translations for the products namespace
  // The hook automatically updates when language changes occur
  const { t, i18n } = useTranslation('products/details');
  
  // Access common translations for shared interface elements
  const { t: commonT } = useTranslation('common');
  
  // Local component state for interactive features
  const [isFavorited, setIsFavorited] = useState(false);
  const [rating, setRating] = useState(0);

  // Handler for favorite toggle with translated feedback
  const handleFavoriteToggle = () => {
    setIsFavorited(!isFavorited);
    // Show translated success message (this would typically use a toast system)
    console.log(isFavorited 
      ? commonT('messages.removed_from_favorites')
      : commonT('messages.added_to_favorites')
    );
  };

  return (
    <motion.div 
      className="bg-white rounded-lg shadow-lg p-6"
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
    >
      {/* Product header with dynamic translations */}
      <header className="mb-4">
        <h2 className="text-2xl font-bold">
          {t('sections.header.title')}
        </h2>
        <p className="text-gray-600">
          {t('sections.header.price_label')}: $99.99
        </p>
      </header>
      
      {/* Interactive rating system */}
      <div className="mb-4">
        <h3 className="font-semibold mb-2">
          {t('sections.rating.title')}
        </h3>
        <div className="flex gap-1">
          {[1, 2, 3, 4, 5].map((star) => (
            <button
              key={star}
              onClick={() => setRating(star)}
              className={`transition-colors ${
                star <= rating ? 'text-yellow-400' : 'text-gray-300'
              }`}
              aria-label={t('sections.rating.star_label', { number: star })}
            >
              <Star className="h-6 w-6 fill-current" />
            </button>
          ))}
        </div>
        {rating > 0 && (
          <p className="text-sm text-gray-600 mt-1">
            {t('sections.rating.thank_you_message')}
          </p>
        )}
      </div>
      
      {/* Action buttons with state-dependent translations */}
      <div className="flex gap-3">
        <button
          onClick={handleFavoriteToggle}
          className={`flex items-center gap-2 px-4 py-2 rounded transition-colors ${
            isFavorited 
              ? 'bg-red-100 text-red-700 hover:bg-red-200' 
              : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
          }`}
        >
          <Heart className={`h-4 w-4 ${isFavorited ? 'fill-current' : ''}`} />
          {isFavorited 
            ? commonT('actions.remove_favorite')
            : commonT('actions.add_favorite')
          }
        </button>
        
        <button className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors">
          <Share className="h-4 w-4" />
          {commonT('actions.share')}
        </button>
      </div>
      
      {/* Language-aware formatting example */}
      <div className="mt-4 pt-4 border-t text-sm text-gray-500">
        <p>
          {t('sections.meta.language_info')}: {i18n.language.toUpperCase()}
        </p>
        <p>
          {t('sections.meta.last_updated')}: {new Date().toLocaleDateString(i18n.language)}
        </p>
      </div>
    </motion.div>
  );
}
```

### Advanced Client Component Patterns

For client components that require complex translation logic, conditional rendering based on language, or integration with external APIs, our system provides advanced patterns that maintain performance while supporting sophisticated internationalization requirements.

```typescript
'use client';

import { useTranslation, Trans } from 'react-i18next';
import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { changeClientLanguage } from '@/lib/i18n/client';

/**
 * Advanced client component demonstrating complex translation patterns
 * This component shows conditional rendering, rich text translations,
 * and integration with routing for language-aware navigation
 */
export default function AdvancedContactForm() {
  const { t, i18n, ready } = useTranslation('forms', { 
    useSuspense: false // Prevent suspense-related hydration issues
  });
  const { t: errorT } = useTranslation('errors');
  const router = useRouter();
  
  // Form state with validation
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    message: ''
  });
  const [errors, setErrors] = useState({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Handle language-specific form validation
  const validateForm = () => {
    const newErrors = {};
    
    if (!formData.name.trim()) {
      newErrors.name = errorT('validation.required', { 
        field: t('fields.name.label') 
      });
    }
    
    if (!formData.email.trim()) {
      newErrors.email = errorT('validation.required', { 
        field: t('fields.email.label') 
      });
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      newErrors.email = errorT('validation.invalid_email');
    }
    
    if (!formData.message.trim()) {
      newErrors.message = errorT('validation.required', { 
        field: t('fields.message.label') 
      });
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Form submission with language-aware feedback
  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!validateForm()) return;
    
    setIsSubmitting(true);
    
    try {
      // Simulate API call that includes language preference
      await fetch('/api/contact', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          ...formData,
          language: i18n.language,
          preferredResponseLanguage: i18n.language
        })
      });
      
      // Show success message and redirect
      alert(t('messages.submission_success'));
      router.push('/thank-you');
      
    } catch (error) {
      alert(errorT('messages.submission_failed'));
    } finally {
      setIsSubmitting(false);
    }
  };

  // Show loading state while translations load
  if (!ready) {
    return (
      <div className="animate-pulse">
        <div className="h-4 bg-gray-200 rounded w-3/4 mb-4"></div>
        <div className="h-4 bg-gray-200 rounded w-1/2 mb-4"></div>
        <div className="h-4 bg-gray-200 rounded w-2/3"></div>
      </div>
    );
  }

  return (
    <div className="max-w-md mx-auto bg-white rounded-lg shadow-lg p-6">
      {/* Form header with rich text translation */}
      <header className="mb-6">
        <h2 className="text-2xl font-bold mb-2">
          {t('contact.header.title')}
        </h2>
        
        {/* Trans component for complex text with embedded links */}
        <p className="text-gray-600">
          <Trans 
            i18nKey="contact.header.description"
            ns="forms"
            components={{
              privacy: <a href="/privacy" className="text-blue-600 underline" />,
              terms: <a href="/terms" className="text-blue-600 underline" />
            }}
          />
        </p>
      </header>
      
      {/* Form with language-aware validation */}
      <form onSubmit={handleSubmit} className="space-y-4">
        {/* Name field */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            {t('fields.name.label')}
            <span className="text-red-500 ml-1">*</span>
          </label>
          <input
            type="text"
            value={formData.name}
            onChange={(e) => setFormData({...formData, name: e.target.value})}
            placeholder={t('fields.name.placeholder')}
            className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${
              errors.name ? 'border-red-500' : 'border-gray-300'
            }`}
          />
          {errors.name && (
            <p className="text-red-500 text-sm mt-1">{errors.name}</p>
          )}
        </div>
        
        {/* Email field */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            {t('fields.email.label')}
            <span className="text-red-500 ml-1">*</span>
          </label>
          <input
            type="email"
            value={formData.email}
            onChange={(e) => setFormData({...formData, email: e.target.value})}
            placeholder={t('fields.email.placeholder')}
            className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${
              errors.email ? 'border-red-500' : 'border-gray-300'
            }`}
          />
          {errors.email && (
            <p className="text-red-500 text-sm mt-1">{errors.email}</p>
          )}
        </div>
        
        {/* Message field */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            {t('fields.message.label')}
            <span className="text-red-500 ml-1">*</span>
          </label>
          <textarea
            value={formData.message}
            onChange={(e) => setFormData({...formData, message: e.target.value})}
            placeholder={t('fields.message.placeholder')}
            rows={4}
            className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${
              errors.message ? 'border-red-500' : 'border-gray-300'
            }`}
          />
          {errors.message && (
            <p className="text-red-500 text-sm mt-1">{errors.message}</p>
          )}
        </div>
        
        {/* Submit button with loading state */}
        <button
          type="submit"
          disabled={isSubmitting}
          className="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {isSubmitting 
            ? t('actions.submitting')
            : t('actions.submit_form')
          }
        </button>
      </form>
    </div>
  );
}
```

### When to Use Client Components for Translations

Client components excel in scenarios requiring user interaction, real-time updates, or dynamic content generation based on user input. They provide the most responsive user experience for interactive elements such as forms, navigation menus, search interfaces, and any content that changes based on user actions or preferences.

Choose client components when you need translations for interactive UI elements, form validation messages, real-time notifications, or any content that must respond immediately to user language preferences. Client-side translation enables seamless language switching without page refreshes and provides the foundation for dynamic, responsive internationalized interfaces.

## Translation Key Naming Conventions

Our naming conventions establish a consistent vocabulary that enables developers to quickly locate and understand translation content. The conventions balance descriptiveness with brevity, ensuring that key names convey meaning while remaining manageable in complex applications.

### Hierarchical Organization Principles

Translation keys follow a hierarchical structure that reflects content organization rather than visual layout. This approach ensures that translations remain stable when design changes occur and makes the translation files self-documenting for both developers and translators.

The primary level typically represents major page sections or functional areas such as `sections`, `navigation`, `forms`, or `meta`. The secondary level identifies specific components or content blocks within those areas. The tertiary level contains individual text elements or related groups of content. This structure creates a logical navigation path through the translation content that mirrors how developers think about application features.

```json
{
  "sections": {
    "hero": {
      "title": "Primary headline text",
      "subtitle": "Supporting description text",
      "cta": {
        "primary": "Main action button text",
        "secondary": "Alternative action text"
      }
    }
  },
  "navigation": {
    "main": {
      "home": "Home page link",
      "products": "Products section link",
      "about": "About page link"
    },
    "breadcrumbs": {
      "home": "Breadcrumb home text",
      "current_page": "Current page indicator"
    }
  }
}
```

### Content Type Classifications

Different types of content require different naming approaches to ensure clarity and prevent confusion. Understanding these classifications helps developers choose appropriate key names and organize translation content effectively.

Action-oriented content such as button labels, form submissions, and navigation links uses verb-based naming that clearly indicates the user action. Status information including error messages, success notifications, and system feedback uses state-based naming that reflects the current application condition. Descriptive content such as feature descriptions, about pages, and product information uses noun-based naming that focuses on the content subject.

```json
{
  "actions": {
    "save_changes": "Save Changes",
    "cancel_operation": "Cancel",
    "delete_item": "Delete",
    "export_data": "Export"
  },
  "status": {
    "loading_content": "Loading...",
    "operation_successful": "Operation completed successfully",
    "connection_error": "Unable to connect to server",
    "validation_failed": "Please check your input"
  },
  "content": {
    "feature_overview": "Comprehensive feature description",
    "company_mission": "Our mission statement",
    "product_benefits": "Key product advantages"
  }
}
```

## Best Practices and Decision Guidelines

Effective internationalization requires thoughtful decisions about when and how to apply different translation strategies. These guidelines help developers make informed choices that balance performance, maintainability, and user experience considerations.

### Choosing Between Server and Client Translation Approaches

The decision between server and client translation approaches depends on content characteristics, performance requirements, and user interaction patterns. Understanding these factors enables developers to optimize both initial page load performance and ongoing user experience.

Server-side translation provides optimal performance for static content that forms the core message of each page. This includes main headings, primary content areas, navigation elements, and any text that contributes to search engine optimization. Server translation ensures that users see fully localized content immediately upon page load, which improves both perceived performance and accessibility.

Client-side translation excels for interactive elements that must respond immediately to user actions or preferences. This includes form validation messages, dynamic status updates, interactive tooltips, and any content that changes based on user input. Client translation enables real-time language switching and provides responsive feedback for user interactions.

Consider hybrid approaches for complex pages that combine static content with interactive elements. Load primary content through server translation to ensure fast initial rendering, then enhance interactive elements with client translation to provide responsive user interactions. This approach maximizes both initial performance and ongoing responsiveness.

### Performance Optimization Strategies

Translation performance affects both initial page load times and ongoing user interactions. Implementing strategic optimizations ensures that internationalization enhances rather than degrades the user experience.

Namespace organization directly impacts performance by controlling how much translation content loads for each page or component. Create focused namespaces that contain only the translations needed for specific functionality, avoiding large monolithic translation files that slow down loading. Use lazy loading for secondary content that appears below the fold or in response to user interactions.

Caching strategies minimize redundant translation loading and improve response times for repeat visitors. Our server-side caching automatically optimizes translation instance creation, while client-side caching through browser storage ensures that translation data persists across page navigations. Configure appropriate cache expiration times that balance content freshness with performance benefits.

### Maintenance and Scalability Considerations

As applications grow, translation management becomes increasingly complex. Establishing robust processes and conventions early prevents technical debt and ensures that internationalization scales effectively with application development.

Version control strategies should treat translation files as first-class application assets that require careful change management. Establish review processes for translation updates that verify both linguistic accuracy and technical correctness. Use clear commit messages that identify the scope and nature of translation changes, enabling easy rollback if issues arise.

Automation opportunities exist throughout the translation workflow, from extracting translation keys during development to validating translation completeness during deployment. Consider integrating translation management tools that provide professional translation services while maintaining technical workflow integration.

## Troubleshooting Common Issues

Even well-designed translation systems encounter challenges during development and deployment. Understanding common issues and their solutions enables developers to quickly resolve problems and maintain smooth internationalization functionality.

### Hydration Mismatches

Hydration mismatches occur when server-rendered content differs from client-rendered content, typically due to language detection differences between server and client environments. These issues manifest as React warnings and can cause visual content flickering during page load.

The most common cause involves inconsistent language detection between middleware and client initialization. Ensure that both server and client systems use the same language detection logic and priority order. Verify that cookies set by the client are properly accessible to server-side code and that middleware headers correctly propagate language information to server components.

Debug hydration issues by logging language detection results at each stage of the rendering process. Compare detected languages between server and client environments, and verify that translation loading produces identical content. Use React development tools to identify specific components where hydration mismatches occur.

### Missing Translation Keys

Missing translation keys create user-facing errors that degrade the application experience. These issues typically result from development workflow gaps or incomplete translation coverage during feature development.

Implement development-time validation that checks for missing translation keys during component rendering. Configure i18next to provide clear fallback behavior that maintains application functionality while highlighting missing translations for developers. Use automated testing to verify translation completeness for critical user paths.

Establish workflow practices that treat translation updates as integral to feature development rather than afterthoughts. Create templates for new translation files that include all required sections, and use linting tools to validate translation key references in component code.

### Language Switching Delays

Language switching delays impact user experience by creating perceived application slowness during language transitions. These delays typically result from inefficient translation loading or unnecessary component re-rendering during language changes.

Optimize language switching by preloading translation data for likely language choices and implementing efficient caching strategies that minimize network requests. Use React optimization techniques such as memo and callback optimization to prevent unnecessary component re-renders during language transitions.

Monitor language switching performance in both development and production environments, measuring the time between user language selection and complete interface updates. Establish performance budgets that ensure language switching remains responsive across different network conditions and device capabilities.

This comprehensive translation system provides the foundation for building truly international applications that serve users effectively regardless of their language preferences. By following these guidelines and understanding the reasoning behind each approach, developers can create maintainable, performant, and user-friendly multilingual experiences that scale with application growth and user needs.

# Language Detection Guide for Next.js 15 App Router

## Understanding the Challenge

Next.js 15 App Router introduced a fundamental shift in how applications handle server-side rendering and static generation. This creates a unique challenge for internationalization: **how do you detect a user's preferred language when some code runs at build time (before any users exist) and other code runs at request time (when users visit your site)?**

Think of it like preparing a restaurant menu. You can pre-print generic menus (static generation), but for personalized dietary recommendations, you need to talk to each customer individually (dynamic rendering).

## Our Three-Method Architecture

We solve this challenge with three specialized detection functions, each designed for specific contexts within Next.js 15's rendering pipeline:

### 🛡️ Method 1: `detectLanguageFromRequest()` - The Gatekeeper

**Purpose:** Used exclusively in middleware to intercept requests before they reach your application pages.

**When to use:** Only in `middleware.ts`

**Why this method exists:** Middleware operates with raw HTTP requests and doesn't have access to Next.js helper functions like `cookies()` or `headers()`. This method works directly with the `NextRequest` object that middleware receives.

```typescript
// src/middleware.ts
import { detectLanguageFromRequest } from '@/lib/i18n/detector';

export async function middleware(request: NextRequest) {
  // ✅ Correct - Use this method ONLY in middleware
  const detectedLanguage = await detectLanguageFromRequest(request);
  
  // Set language info for downstream components
  const response = NextResponse.next();
  response.headers.set('x-language', detectedLanguage);
  return response;
}
```

**What makes it special:** This method has the highest reliability because it operates on the actual HTTP request with full access to cookies and headers.

### 🏠 Method 2: `detectLanguageFromHeaders()` - The Reliable Default

**Purpose:** Safe language detection for most pages and components that gracefully handles both build time and runtime scenarios.

**When to use:** 
- Root layout (`layout.tsx`)
- Most page components
- Server components where speed matters more than 100% accuracy
- Any component that should work during static generation

**Why this method exists:** Next.js 15 tries to statically generate pages at build time for performance. During this process, user-specific data like cookies doesn't exist yet. This method includes error handling that allows static generation to succeed by falling back to a default language when user data isn't available.

```typescript
// src/app/layout.tsx
import { detectLanguageFromHeaders } from '@/lib/i18n/detector';

export async function generateMetadata(): Promise<Metadata> {
  // ✅ Correct - Safe for static generation
  // During build: returns fallback language ('en')
  // During runtime: returns user's actual preferred language
  const language = await detectLanguageFromHeaders();
  const { t } = await getServerTranslation(language, 'common');
  
  return {
    title: t('site.name'),
    description: t('site.description')
  };
}

// src/app/page.tsx
export default async function HomePage() {
  // ✅ Correct - Most users get correct language
  // New users get fallback until they use language switcher
  const language = await detectLanguageFromHeaders();
  const { t } = await getServerTranslation(language, 'common');
  
  return <h1>{t('welcome')}</h1>;
}
```

**The trade-off:** This method prioritizes application stability over perfect accuracy. It's designed with the philosophy that "a working app with occasionally incorrect language is better than a broken app."

### 🎯 Method 3: `detectLanguageFromCookiesOnly()` - The Precision Tool

**Purpose:** Maximum accuracy language detection for critical pages where getting the language wrong would significantly impact user experience.

**When to use:**
- User dashboards
- Account settings pages
- Order details and checkout flows
- Any page with personalized or sensitive content

**Why this method exists:** Some pages are so important that you need 100% accurate language detection, even if it means sacrificing static generation performance. This method reads directly from cookies with no fallbacks, ensuring maximum precision.

```typescript
// src/app/dashboard/page.tsx
export const dynamic = 'force-dynamic'; // Required! Tells Next.js this page needs runtime rendering

export default async function DashboardPage() {
  // ✅ Correct - Critical page needs precise language
  // User must see their account info in the correct language
  const language = await detectLanguageFromCookiesOnly();
  const { t } = await getServerTranslation(language, 'dashboard');
  
  return (
    <div>
      <h1>{t('my_account')}</h1>
      <p>{t('account_balance')}: {user.balance}</p>
    </div>
  );
}

// src/app/checkout/page.tsx
export const dynamic = 'force-dynamic'; // Required!

export default async function CheckoutPage() {
  // ✅ Correct - Legal terms must be in correct language
  const language = await detectLanguageFromCookiesOnly();
  return <CheckoutForm language={language} />;
}
```

**Important requirement:** You MUST add `export const dynamic = 'force-dynamic'` when using this method. This tells Next.js to skip static generation for this page and always render it server-side for each request.

## Decision Tree for Developers

When writing a new component or page, ask yourself these questions:

### Step 1: What context am I in?
- **If you're in `middleware.ts`** → Use `detectLanguageFromRequest()`
- **If you're in any other file** → Continue to Step 2

### Step 2: How critical is language accuracy?
- **Critical** (user account, payments, legal docs) → Use `detectLanguageFromCookiesOnly()` + add `export const dynamic = 'force-dynamic'`
- **Not critical** (marketing pages, general content) → Use `detectLanguageFromHeaders()`

### Step 3: Are you unsure?
- **When in doubt** → Use `detectLanguageFromHeaders()` (the safe default)

## Real-World Examples

### E-commerce Site Architecture

```typescript
// Root layout - not critical, needs to work during build
// src/app/layout.tsx
const language = await detectLanguageFromHeaders();

// Product catalog - marketing content, not critical
// src/app/products/page.tsx
const language = await detectLanguageFromHeaders();

// Individual product - static content, not critical
// src/app/products/[id]/page.tsx
const language = await detectLanguageFromHeaders();

// Shopping cart - CRITICAL, user needs to understand prices
// src/app/cart/page.tsx
export const dynamic = 'force-dynamic';
const language = await detectLanguageFromCookiesOnly();

// Checkout process - CRITICAL, legal implications
// src/app/checkout/page.tsx
export const dynamic = 'force-dynamic';
const language = await detectLanguageFromCookiesOnly();
```

### SaaS Application Architecture

```typescript
// Landing page - marketing content
// src/app/page.tsx
const language = await detectLanguageFromHeaders();

// Pricing page - marketing content
// src/app/pricing/page.tsx
const language = await detectLanguageFromHeaders();

// User dashboard - CRITICAL, personalized content
// src/app/dashboard/page.tsx
export const dynamic = 'force-dynamic';
const language = await detectLanguageFromCookiesOnly();

// Account settings - CRITICAL, user preferences
// src/app/settings/page.tsx
export const dynamic = 'force-dynamic';
const language = await detectLanguageFromCookiesOnly();
```

## Why This Architecture Exists

This three-method approach directly addresses Next.js 15's rendering architecture:

### Static Generation Benefits
Next.js 15 can pre-generate many pages at build time, making them load incredibly fast. Pages using `detectLanguageFromHeaders()` can be statically generated because this method gracefully handles the absence of user data during build time.

### Dynamic Rendering When Needed
Critical pages using `detectLanguageFromCookiesOnly()` opt into dynamic rendering, ensuring they always have access to fresh user data. The performance cost is justified by the accuracy requirement.

### Middleware Integration
The middleware method works with Next.js's request interception system, allowing you to make language decisions before the main application logic runs.

## Performance Implications

**Static pages** (using `detectLanguageFromHeaders()`):
- ✅ Fast initial load
- ✅ Good SEO
- ✅ Lower server costs
- ⚠️ Might show wrong language initially for new users

**Dynamic pages** (using `detectLanguageFromCookiesOnly()`):
- ✅ Perfect language accuracy
- ✅ Real-time user data
- ⚠️ Slower initial load
- ⚠️ Higher server costs

## Common Mistakes to Avoid

### ❌ Wrong: Using middleware method outside middleware
```typescript
// This will cause an error - NextRequest only exists in middleware
const language = await detectLanguageFromRequest(request);
```

### ❌ Wrong: Using precision method without force-dynamic
```typescript
// This might cause build errors
const language = await detectLanguageFromCookiesOnly(); // Missing: export const dynamic = 'force-dynamic'
```

### ❌ Wrong: Using precision method everywhere
```typescript
// This makes everything slow unnecessarily
export const dynamic = 'force-dynamic'; // Only use when truly needed
```

### ✅ Correct: Match method to use case
```typescript
// Layout and general pages
const language = await detectLanguageFromHeaders();

// Critical user-specific pages
export const dynamic = 'force-dynamic';
const language = await detectLanguageFromCookiesOnly();
```

## Testing Your Implementation

To verify your language detection is working correctly:

1. **Build test**: Run `npm run build` - it should complete without errors
2. **Static pages test**: Visit general pages as a new user - should show fallback language initially
3. **Dynamic pages test**: Visit user-specific pages - should always show correct language
4. **Language switcher test**: Change language and verify it persists across page reloads

This architecture ensures Next.js 15 application is both performant and accurate, using the right tool for each specific context within the framework's rendering pipeline.
