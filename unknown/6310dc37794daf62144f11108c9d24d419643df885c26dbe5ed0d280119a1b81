import React from 'react';
import type { Language } from '@/types';
import I18nProvider from './i18n-provider';
import ThemeProvider from './theme-provider';
import { Toaster } from '@/components/ui/sonner';

type Props = {
  children: React.ReactNode;
  detectedLanguage: string;
};

export default async function AppProviders({
  children,
  detectedLanguage,
}: Props) {
  // Convert string to valid Language type, defaulting to "en" if not valid
  const language: Language = detectedLanguage === 'cs' ? 'cs' : 'en';

  return (
    <I18nProvider initialLanguage={language}>
      <ThemeProvider
        attribute="class"
        defaultTheme="system"
        enableSystem
        disableTransitionOnChange
      >
        {children}
        <Toaster />
      </ThemeProvider>
    </I18nProvider>
  );
}
