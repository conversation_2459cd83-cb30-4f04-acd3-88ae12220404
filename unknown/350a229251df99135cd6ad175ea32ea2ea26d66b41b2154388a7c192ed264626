# Ignorovať node_modules a iné z<PERSON>ti
node_modules
npm-debug.log
yarn-error.log
.pnpm-debug.log

# Ignorovať súbory a adresáre vytvorené editorom
*.vscode
*.idea
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# Ignorovať súbory logov
logs
*.log
*.log.*

# Ignorovať súbory env
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Ignorovať súbory dočasného buildu a cache
.next
out
build
dist
.cache
coverage

# Ignorovať súbory a adresáre vytvorené pri testovaní
jest
jest.config.js
jest.setup.js
__tests__
__mocks__
__snapshots__

# Ignorovať súbory a adresáre vytvorené pri lintingu
.eslintcache

# Ignorovať rôzne konfiguračné a generované súbory
*.DS_Store
*.dockerignore
*.git
*.gitignore