import React from 'react';
import type { Metadata } from 'next';
import { detectLanguageFromHeaders } from '@/lib/i18n/detector';
import { getServerTranslation } from '@/lib/i18n/server';
import About from '@/components/about';

export async function generateMetadata(): Promise<Metadata> {
  const language = await detectLanguageFromHeaders();
  const { t } = await getServerTranslation(language, 'about');

  return {
    title: t('site.title'),
    description: t('site.description'),
    openGraph: {
      title: t('site.title'),
      description: t('site.description'),
      type: 'website',
    },
    twitter: {
      card: 'summary_large_image',
      title: t('site.title'),
      description: t('site.description'),
    },
  };
}

const AboutPage = () => {
  return <About />;
};

export default AboutPage;
