'use client';

import React, { useEffect, useRef, useState } from 'react';
import { motion } from 'motion/react';
import { FileText, CheckCircle } from 'lucide-react';
import { Badge } from '@/components/ui/badge';

type Props = {
  position: string;
  delay: number;
  title: string;
  type: string;
};

const FloatingDocumentCard = ({ position, delay, title, type }: Props) => {
  const cardRef = useRef<HTMLDivElement>(null);
  const [dimensions, setDimensions] = useState({ width: 0, height: 0 });
  const [touchState, setTouchState] = useState({
    isActive: false,
    startX: 0,
    startY: 0,
    currentX: 0,
    currentY: 0,
    scale: 1,
    rotation: { x: 0, y: 0 },
    lastTap: 0,
  });

  // Simple responsive monitoring
  useEffect(() => {
    const handleResize = () => {
      setDimensions({
        width: window.innerWidth,
        height: window.innerHeight,
      });
    };

    handleResize();
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // Simple haptic feedback
  const triggerHaptic = (type: 'light' | 'medium' | 'heavy' = 'light') => {
    if ('vibrate' in navigator) {
      const patterns = { light: [10], medium: [20], heavy: [30, 10, 30] };
      navigator.vibrate(patterns[type]);
    }
  };

  // Touch handlers
  const handleTouchStart = (e: React.TouchEvent) => {
    e.preventDefault();
    const touch = e.touches[0];
    const now = Date.now();

    if (now - touchState.lastTap < 300) {
      handleDoubleTap();
      return;
    }

    setTouchState((prev) => ({
      ...prev,
      isActive: true,
      startX: touch.clientX,
      startY: touch.clientY,
      currentX: touch.clientX,
      currentY: touch.clientY,
      lastTap: now,
    }));

    triggerHaptic('light');
  };

  const handleTouchMove = (e: React.TouchEvent) => {
    e.preventDefault();
    if (!touchState.isActive || !cardRef.current) return;

    const touch = e.touches[0];
    const rect = cardRef.current.getBoundingClientRect();
    const centerX = rect.left + rect.width / 2;
    const centerY = rect.top + rect.height / 2;

    const rotateX = Math.max(
      -12,
      Math.min(12, ((touch.clientY - centerY) / rect.height) * -15)
    );
    const rotateY = Math.max(
      -12,
      Math.min(12, ((touch.clientX - centerX) / rect.width) * 15)
    );

    const dragX = touch.clientX - touchState.startX;
    const dragY = touch.clientY - touchState.startY;
    const dragDistance = Math.sqrt(dragX * dragX + dragY * dragY);

    const dynamicScale = Math.min(1.15, 1 + dragDistance / 500);

    if (cardRef.current) {
      cardRef.current.style.transform = `
        perspective(1000px) 
        rotateX(${rotateX}deg) 
        rotateY(${rotateY}deg) 
        translateZ(20px)
        scale(${dynamicScale})
        translateX(${dragX * 0.1}px)
        translateY(${dragY * 0.1}px)
      `;
    }

    if (dragDistance > 50 && dragDistance % 25 < 5) {
      triggerHaptic('light');
    }
  };

  const handleTouchEnd = (e: React.TouchEvent) => {
    e.preventDefault();
    if (!cardRef.current) return;

    cardRef.current.style.transition =
      'transform 0.6s cubic-bezier(0.34, 1.56, 0.64, 1)';
    cardRef.current.style.transform = 'scale(1) rotateX(0deg) rotateY(0deg)';

    setTimeout(() => {
      if (cardRef.current) cardRef.current.style.transition = '';
    }, 600);

    setTouchState((prev) => ({
      ...prev,
      isActive: false,
      scale: 1,
      rotation: { x: 0, y: 0 },
    }));

    triggerHaptic('medium');
  };

  const handleDoubleTap = () => {
    if (!cardRef.current) return;
    triggerHaptic('heavy');
    cardRef.current.style.transition =
      'transform 0.8s cubic-bezier(0.68, -0.55, 0.265, 1.55)';
    cardRef.current.style.transform = 'rotateY(180deg) scale(1.1)';
    setTimeout(() => {
      if (cardRef.current)
        cardRef.current.style.transform = 'rotateY(0deg) scale(1)';
    }, 400);
    setTimeout(() => {
      if (cardRef.current) cardRef.current.style.transition = '';
    }, 800);
  };

  // Responsive size classes
  const isMobile = dimensions.width < 768;
  const isTablet = dimensions.width >= 768 && dimensions.width < 1024;

  const sizeClasses = isMobile
    ? 'h-64 w-48 p-4'
    : isTablet
      ? 'h-72 w-56 p-5'
      : 'h-80 w-64 p-6';

  return (
    <motion.div
      ref={cardRef}
      className={`group absolute transform-gpu touch-manipulation rounded-2xl border border-blue-400/40 bg-gradient-to-r from-blue-600/80 via-cyan-500/70 to-blue-600/80 shadow-2xl backdrop-blur-md transition-all duration-300 ease-out will-change-transform select-none hover:border-blue-300/60 hover:shadow-[0_20px_40px_-12px_rgba(59,130,246,0.3)] dark:border-white/20 dark:bg-white/10 dark:hover:border-white/30 dark:hover:shadow-[0_20px_40px_-12px_rgba(255,255,255,0.1)] ${sizeClasses} ${position} `}
      initial={{ opacity: 0, scale: 0.8, y: 50 }}
      animate={{ opacity: 1, scale: 1, y: 0 }}
      transition={{
        duration: 0.8,
        delay: delay * 0.2,
        ease: [0.34, 1.56, 0.64, 1],
      }}
      whileHover={{
        scale: 1.03,
        y: -8,
        transition: { duration: 0.3 },
      }}
      style={{
        perspective: '1000px',
        transformStyle: 'preserve-3d',
        touchAction: 'none',
      }}
      onTouchStart={handleTouchStart}
      onTouchMove={handleTouchMove}
      onTouchEnd={handleTouchEnd}
    >
      {/* Simple floating animation */}
      <motion.div
        className="absolute inset-0"
        animate={{
          y: [0, -8, 0],
          rotateY: [0, 3, 0],
        }}
        transition={{
          duration: 4 + delay,
          repeat: Infinity,
          ease: 'easeInOut',
        }}
      />

      <div className="relative z-10 mb-4 flex items-center justify-between">
        <Badge
          variant="secondary"
          className="border-blue-400/30 bg-blue-500/20 text-blue-300 transition-transform duration-200 ease-out group-hover:scale-105"
        >
          {type}
        </Badge>
        <FileText className="h-5 w-5 text-blue-300 transition-all duration-200 ease-out group-hover:scale-110 group-hover:text-blue-200" />
      </div>

      <h3 className="relative z-10 mb-3 font-semibold text-white transition-transform duration-200 ease-out group-hover:scale-105">
        {title}
      </h3>

      <div className="relative z-10 space-y-2 transition-all duration-200 ease-out group-hover:space-y-2.5">
        {[...Array(8)].map((_, i) => (
          <motion.div
            key={i}
            initial={{ width: 0, opacity: 0 }}
            animate={{
              width: i % 3 === 0 ? '100%' : i % 2 === 0 ? '75%' : '50%',
              opacity: 1,
            }}
            transition={{
              duration: 0.6,
              delay: delay * 0.2 + i * 0.1,
              ease: 'easeOut',
            }}
            className="h-2 rounded bg-white/50 transition-colors duration-150 ease-out group-hover:bg-white/60"
          />
        ))}
      </div>

      <div className="absolute right-4 bottom-4 z-10">
        <motion.div
          className="flex h-8 w-8 items-center justify-center rounded-full bg-green-500/30 transition-all duration-200 ease-out group-hover:bg-green-400/40"
          whileHover={{ scale: 1.1 }}
        >
          <CheckCircle className="h-4 w-4 text-green-300 transition-all duration-200 ease-out group-hover:text-green-200" />
        </motion.div>
      </div>

      {/* Simple overlay effects */}
      <div className="pointer-events-none absolute inset-0 rounded-2xl bg-gradient-to-r from-blue-500/8 via-cyan-400/8 to-blue-500/8 opacity-0 transition-opacity duration-300 ease-out group-hover:opacity-100" />

      {/* Touch indicator */}
      <motion.div
        className="absolute top-2 left-2 h-2 w-2 rounded-full"
        animate={{
          backgroundColor: touchState.isActive ? '#22d3ee' : 'transparent',
          scale: touchState.isActive ? [1, 1.2, 1] : 1,
        }}
        transition={{ duration: 0.3 }}
      />
    </motion.div>
  );
};

export default FloatingDocumentCard;
