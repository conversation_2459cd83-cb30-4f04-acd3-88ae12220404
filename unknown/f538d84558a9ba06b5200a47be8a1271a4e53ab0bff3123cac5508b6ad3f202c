import type { Variants } from 'motion/react';

/**
 * Container variants for footer animation
 */
export const containerVariants: Variants = {
  hidden: {
    opacity: 0,
  },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1,
      delayChildren: 0.2,
    },
  },
};

/**
 * Item variants for footer animation
 */
export const itemVariants: Variants = {
  hidden: {
    opacity: 0,
    y: 20,
  },
  visible: {
    opacity: 1,
    y: 0,
    transition: {
      duration: 0.6,
      ease: [0.25, 0.46, 0.45, 0.94] as const,
    },
  },
};

/**
 * Footer link interface
 */
export interface FooterLegalLink {
  key: string;
  href: string;
  external?: boolean;
  target?: '_blank' | '_self';
}

/**
 * Footer section link interface
 */
export interface FooterSectionLink {
  key: string;
  href: string;
  external?: boolean;
  target?: '_blank' | '_self';
}

/**
 * Footer legal links configuration
 */
export const footerLegalLinks: FooterLegalLink[] = [
  {
    key: 'privacy',
    href: '/privacy-policy',
    external: false,
  },
  {
    key: 'terms',
    href: '/terms-of-service',
    external: false,
  },
  {
    key: 'cookies',
    href: '/cookies',
    external: false,
  },
];

/**
 * About section links configuration
 */
export const aboutSectionLinks: FooterSectionLink[] = [
  {
    key: 'about-us',
    href: '/about',
    external: false,
  },
  {
    key: 'contact',
    href: '/contact',
    external: false,
  },
  {
    key: 'faq',
    href: '/faq',
    external: false,
  },
];

/**
 * Products section links configuration
 * All product links follow the pattern /products/{key}
 */
export const productSectionLinks: FooterSectionLink[] = [
  {
    key: 'mailroom',
    href: '/products/mailroom',
    external: false,
  },
  {
    key: 'received-invoices',
    href: '/products/received-invoices',
    external: false,
  },
  {
    key: 'issued-invoices',
    href: '/products/issued-invoices',
    external: false,
  },
  {
    key: 'contracts',
    href: '/products/contracts',
    external: false,
  },
  {
    key: 'requisitions',
    href: '/products/requisitions',
    external: false,
  },
  {
    key: 'directives',
    href: '/products/directives',
    external: false,
  },
  {
    key: 'digital-archive',
    href: '/products/digital-archive',
    external: false,
  },
  {
    key: 'identity-management',
    href: '/products/identity-management',
    external: false,
  },
];

/**
 * Helper function to get link attributes based on configuration
 */
export const getLinkAttributes = (
  link: FooterLegalLink | FooterSectionLink
) => {
  const attributes: Record<string, string> = {
    href: link.href,
  };

  if (link.external) {
    attributes.target = link.target || '_blank';
    attributes.rel = 'noopener noreferrer';
  }

  return attributes;
};
