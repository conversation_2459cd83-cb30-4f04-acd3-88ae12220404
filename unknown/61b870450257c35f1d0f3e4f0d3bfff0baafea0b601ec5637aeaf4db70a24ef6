'use client';

import { ReactNode, useEffect, useState } from 'react';
import { I18nextProvider } from 'react-i18next';
import { initClientI18n } from '@/lib/i18n/client';
import { type Language } from '@/lib/i18n/settings';
import type { i18n } from 'i18next';
import debug from 'debug';

const langLog = debug('i18n:provider:lang');

interface I18nProviderProps {
  children: ReactNode;
  initialLanguage: Language;

  // Optional: pre-loaded resources from server for hydration
  initialResources?: Record<string, Record<string, Record<string, unknown>>>;
}

/**
 * Client-side i18next provider according to react-i18next documentation
 * Ensures proper hydration and synchronization of server/client state
 */
export default function I18nProvider({
  children,
  initialLanguage,
  initialResources,
}: I18nProviderProps) {
  const [i18nInstance, setI18nInstance] = useState<i18n | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    let isMounted = true;

    const setupI18n = async () => {
      try {
        langLog(`🎯 Setting up client i18n with language: ${initialLanguage}`);

        // Initialize the i18next client with the server language
        const instance = await initClientI18n(initialLanguage);

        // If we have pre-loaded resources, add them to the instance
        if (initialResources) {
          langLog('📦 Loading pre-loaded resources');

          Object.entries(initialResources).forEach(([language, namespaces]) => {
            Object.entries(
              namespaces as Record<string, Record<string, unknown>>
            ).forEach(([namespace, data]) => {
              instance.addResourceBundle(language, namespace, data, true, true);
            });
          });
        }

        // Make sure we're on the right language
        if (instance.language !== initialLanguage) {
          await instance.changeLanguage(initialLanguage);
        }

        // Set instance only if component is still mounted
        if (isMounted) {
          setI18nInstance(instance);
          setIsLoading(false);
        }
      } catch (error) {
        console.error('Failed to setup i18n:', error);
        if (isMounted) {
          setIsLoading(false);
        }
      }
    };

    setupI18n();

    // Cleanup
    return () => {
      isMounted = false;
    };
  }, [initialLanguage, initialResources]);

  // Loading state with fallback content
  if (isLoading || !i18nInstance) {
    return (
      <div className="flex min-h-screen items-center justify-center">
        <div className="border-primary h-8 w-8 animate-spin rounded-full border-b-2"></div>
      </div>
    );
  }

  return <I18nextProvider i18n={i18nInstance}>{children}</I18nextProvider>;
}
