import React from 'react';
import Link from 'next/link';

import { detectLanguageFromHeaders } from '@/lib/i18n/detector';
import { getServerTranslation } from '@/lib/i18n/server';

import Nav from './nav';

import Brand from '@/components/brand';
import ThemeSwitcher from '@/components/theme-switcher';
import LanguageSwitcher from '@/components/language-switcher';

import { Button } from '@/components/ui/button';

const Navbar = async () => {
  const language = await detectLanguageFromHeaders();
  const { t } = await getServerTranslation(language, 'common');

  return (
    <header className="fixed top-0 z-40 w-full border-b border-white/20 bg-white/10 backdrop-blur-md transition-all duration-300 dark:border-white/10 dark:bg-black/10">
      <div className="container mx-auto px-6 py-4">
        {/* Grid layout for perfect centering */}
        <div className="grid grid-cols-3 items-center">
          {/* Left section - Brand */}
          <div className="flex justify-start">
            <Link href="/" className="flex items-center space-x-2">
              <Brand />
            </Link>
          </div>

          {/* Center section - Navigation (always centered) */}
          <div className="flex justify-center">
            <Nav />
          </div>

          {/* Right section - Controls */}
          <div className="flex items-center justify-end space-x-5">
            <ThemeSwitcher />
            <LanguageSwitcher />
            <Link href="/contact">
              <Button className="ms-3 bg-gradient-to-r from-blue-600 to-cyan-500 font-bold tracking-wide text-white shadow-lg transition-all duration-250 ease-out will-change-transform hover:scale-105 hover:from-blue-700 hover:to-cyan-600 hover:shadow-xl">
                {t('navigation.button.contact')}
              </Button>
            </Link>
          </div>
        </div>
      </div>
    </header>
  );
};

export default Navbar;
