import * as React from 'react';
import { US, CZ } from 'country-flag-icons/react/3x2';

type Props = {
  countryCode: string;
  className?: string;
};

const FlagIcon = ({ countryCode, className = '' }: Props) => {
  const flags: Record<string, React.ComponentType<{ className?: string }>> = {
    en: US, // US flag for English
    cs: CZ, // Czech flag for Czech
  };

  const FlagComponent = flags[countryCode];

  if (!FlagComponent) {
    // Fallback for unsupported languages
    return (
      <div
        className={`flex h-4 w-6 items-center justify-center rounded-sm bg-gray-200 dark:bg-gray-700 ${className}`}
      >
        <span className="text-[8px] font-bold text-gray-600 dark:text-gray-400">
          {countryCode.toUpperCase()}
        </span>
      </div>
    );
  }

  return <FlagComponent className={`h-4 w-6 rounded-sm ${className}`} />;
};

export default FlagIcon;
