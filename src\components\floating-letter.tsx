'use client';

import { motion } from 'motion/react';

const FloatingLetter = ({
  delay,
  letter,
  startX,
  startY,
  size = 'text-sm',
  color = 'text-blue-600',
}: {
  delay: number;
  letter: string;
  startX: string;
  startY: string;
  size?: string;
  color?: string;
}) => (
  <motion.div
    className={`absolute font-bold ${size} ${color} pointer-events-none opacity-70 select-none dark:text-sky-400 dark:opacity-60`}
    style={{
      left: startX,
      top: startY,
    }}
    animate={{
      y: [0, -30, -60, -30, 0],
      x: [0, 15, -10, 5, 0],
      opacity: [0.7, 0.9, 0.5, 0.8, 0.7],
      scale: [1, 1.2, 0.8, 1.1, 1],
      rotate: [0, 5, -5, 3, 0],
    }}
    transition={{
      duration: 8,
      delay,
      repeat: Infinity,
      ease: 'easeInOut',
    }}
  >
    {letter}
  </motion.div>
);

export default FloatingLetter;
