'use client';

import React from 'react';
import { useTranslation, Trans } from 'react-i18next';
import { motion } from 'motion/react';
import {
  Building2,
  Cog,
  DollarSign,
  Database,
  Calculator,
  Check,
} from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent } from '@/components/ui/card';
import GradientHighlighter from '@/components/gradient-highlighter';

const TechnologyIntegrations = () => {
  const { t } = useTranslation('about');

  const partners = [
    {
      key: 'sap',
      icon: Building2,
      color: 'text-blue-600 dark:text-blue-400',
      bgColor: 'bg-blue-100 dark:bg-blue-900/30',
      gradient: 'from-blue-500/20 to-blue-600/20',
    },
    {
      key: 'qi',
      icon: Cog,
      color: 'text-green-600 dark:text-green-400',
      bgColor: 'bg-green-100 dark:bg-green-900/30',
      gradient: 'from-green-500/20 to-green-600/20',
    },
    {
      key: 'money',
      icon: DollarSign,
      color: 'text-emerald-600 dark:text-emerald-400',
      bgColor: 'bg-emerald-100 dark:bg-emerald-900/30',
      gradient: 'from-emerald-500/20 to-emerald-600/20',
    },
    {
      key: 'abra',
      icon: Database,
      color: 'text-purple-600 dark:text-purple-400',
      bgColor: 'bg-purple-100 dark:bg-purple-900/30',
      gradient: 'from-purple-500/20 to-purple-600/20',
    },
    {
      key: 'pohoda',
      icon: Calculator,
      color: 'text-orange-600 dark:text-orange-400',
      bgColor: 'bg-orange-100 dark:bg-orange-900/30',
      gradient: 'from-orange-500/20 to-orange-600/20',
    },
  ];

  return (
    <section className="from-background to-muted/30 bg-gradient-to-b px-4 py-20">
      <div className="mx-auto max-w-7xl">
        {/* Section header */}
        <div className="mb-16 text-center">
          <Trans
            i18nKey="technology.title"
            ns="about"
            parent={motion.h2}
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true, margin: '-100px' }}
            transition={{ duration: 0.8 }}
            className="mb-4 text-3xl font-bold text-gray-900 text-shadow-2xs sm:text-4xl md:text-5xl dark:text-white"
            components={{
              highlighted: <GradientHighlighter />,
            }}
          />
          <motion.p
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true, margin: '-100px' }}
            transition={{ duration: 0.8, delay: 0.2 }}
            className="stext-primary mb-4 text-xl font-semibold text-shadow-xs"
          >
            {t('technology.subtitle')}
          </motion.p>
          <motion.p
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true, margin: '-100px' }}
            transition={{ duration: 0.8, delay: 0.4 }}
            className="mx-auto max-w-3xl text-lg text-gray-700 dark:text-gray-300"
          >
            {t('technology.description')}
          </motion.p>
        </div>

        {/* Partners grid */}
        <div className="mb-16 grid gap-6 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5">
          {partners.map((partner, index) => {
            const IconComponent = partner.icon;
            return (
              <motion.div
                key={partner.key}
                initial={{ opacity: 0, y: 50, scale: 0.9 }}
                whileInView={{ opacity: 1, y: 0, scale: 1 }}
                viewport={{ once: true, margin: '-100px' }}
                transition={{ duration: 0.8, delay: index * 0.1 }}
                whileHover={{ y: -8, scale: 1.05 }}
                className="group"
              >
                <Card className="border-border/50 bg-card/80 group-hover:border-primary/30 h-full overflow-hidden backdrop-blur-sm transition-all duration-500 hover:shadow-xl">
                  <CardContent className="p-6 text-center">
                    {/* Partner icon */}
                    <div
                      className={`mx-auto mb-4 h-16 w-16 rounded-2xl ${partner.bgColor} flex items-center justify-center transition-all duration-300 group-hover:scale-110 group-hover:rotate-6`}
                    >
                      <IconComponent className={`h-8 w-8 ${partner.color}`} />
                    </div>

                    {/* Partner name */}
                    <h3 className="mb-3 text-xl font-bold text-gray-900 dark:text-white">
                      {t(`technology.partners.${partner.key}.name`)}
                    </h3>

                    {/* Partner description */}
                    <p className="text-sm leading-relaxed text-gray-600 dark:text-gray-400">
                      {t(`technology.partners.${partner.key}.description`)}
                    </p>

                    {/* Integration badge */}
                    <div className="mt-4">
                      <Badge
                        variant="outline"
                        className={`${partner.color} border-current bg-current/10 text-xs`}
                      >
                        Integrated
                      </Badge>
                    </div>
                  </CardContent>

                  {/* Hover effect overlay */}
                  <div
                    className={`absolute inset-0 bg-gradient-to-br ${partner.gradient} pointer-events-none opacity-0 transition-opacity duration-500 group-hover:opacity-100`}
                  />
                </Card>
              </motion.div>
            );
          })}
        </div>

        {/* Benefits section */}
        <motion.div
          initial={{ opacity: 0, y: 50 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true, margin: '-100px' }}
          transition={{ duration: 0.8, delay: 0.6 }}
          className="mb-12 text-center"
        >
          <h3 className="mb-8 text-3xl font-bold text-gray-900 dark:text-white">
            {t('technology.benefits.title')}
          </h3>
        </motion.div>

        {/* Benefits grid */}
        <div className="grid gap-4 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5">
          {(() => {
            const benefits = t('technology.benefits.items', {
              returnObjects: true,
            });
            const benefitsArray = Array.isArray(benefits) ? benefits : [];
            return benefitsArray.map((benefit, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true, margin: '-100px' }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                whileHover={{ scale: 1.05 }}
                className="group"
              >
                <Card className="border-border/50 bg-card/80 group-hover:border-primary/30 h-full backdrop-blur-sm transition-all duration-300 hover:shadow-lg">
                  <CardContent className="p-6 text-center">
                    <div className="from-primary to-secondary mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-gradient-to-r">
                      <Check className="h-6 w-6 text-white" />
                    </div>
                    <p className="leading-relaxed font-medium text-gray-700 dark:text-gray-300">
                      {benefit}
                    </p>
                  </CardContent>
                </Card>
              </motion.div>
            ));
          })()}
        </div>

        {/* Bottom integration showcase */}
        <motion.div
          initial={{ opacity: 0, y: 50 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true, margin: '-100px' }}
          transition={{ duration: 0.8, delay: 0.8 }}
          className="mt-16"
        >
          <Card className="border-primary/20 from-primary/5 to-secondary/5 overflow-hidden bg-gradient-to-br">
            <CardContent className="p-8 text-center">
              <h3 className="mb-4 text-2xl font-bold text-gray-900 dark:text-white">
                Seamless Integration Ecosystem
              </h3>
              <p className="mx-auto mb-6 max-w-2xl text-gray-700 dark:text-gray-300">
                Our platform connects with your existing business systems to
                create a unified, efficient workflow that eliminates data silos
                and reduces manual processes.
              </p>

              {/* Integration flow visualization */}
              <div className="flex flex-wrap items-center justify-center gap-4">
                <Badge
                  variant="outline"
                  className="text-primary border-primary/30 bg-primary/10"
                >
                  Your ERP System
                </Badge>
                <div className="from-primary to-secondary hidden h-px w-8 bg-gradient-to-r sm:block" />
                <Badge
                  variant="outline"
                  className="text-secondary border-secondary/30 bg-secondary/10"
                >
                  Centris Platform
                </Badge>
                <div className="from-secondary to-primary hidden h-px w-8 bg-gradient-to-r sm:block" />
                <Badge
                  variant="outline"
                  className="border-cyan-600/30 bg-cyan-600/10 text-cyan-600"
                >
                  Enhanced Workflow
                </Badge>
              </div>
            </CardContent>
          </Card>
        </motion.div>
      </div>
    </section>
  );
};

export default TechnologyIntegrations;
