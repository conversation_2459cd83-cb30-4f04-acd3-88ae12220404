import React from 'react';

type Props = {
  /**
   * Child components to be rendered inside main content area
   */
  children: React.ReactNode;

  /**
   * Optional custom CSS classes for styling
   */
  className?: string;

  /**
   * Optional ID attribute for the main element
   */
  id?: string;

  /**
   * Optional ref forwarding (React 19 - ref as prop pattern)
   * No need for forwardRef wrapper in React 19
   */
  ref?: React.Ref<HTMLElement>;

  /**
   * Optional ARIA label for enhanced accessibility
   */
  'aria-label'?: string;

  /**
   * Optional ARIA labelledby for enhanced accessibility
   */
  'aria-labelledby'?: string;
};

/**
 * MainContent component
 *
 * Features:
 * - Semantic HTML using <main> element for accessibility
 * - React 19 ref-as-prop pattern (no forwardRef needed)
 * - TypeScript with proper interface definitions
 * - Accessibility best practices with ARIA support
 * - Flexible styling with optional className
 * - Follows 2025 modern React/Next.js standards
 *
 * @param props MainContentProps interface
 * @returns JSX.Element with semantic main content structure
 */
export const MainContent = ({
  children,
  className = '',
  id,
  ref,
  'aria-label': ariaLabel,
  'aria-labelledby': ariaLabelledby,
  ...otherProps
}: Props) => {
  return (
    <main
      ref={ref}
      id={id}
      className={`main-content ${className}`.trim()}
      aria-label={ariaLabel}
      aria-labelledby={ariaLabelledby}
      role="main" // Explicit role for enhanced screen reader support
      {...otherProps}
    >
      {children}
    </main>
  );
};

export default MainContent;
