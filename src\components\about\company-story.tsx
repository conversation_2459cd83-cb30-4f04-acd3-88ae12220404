'use client';

import React from 'react';
import { useTranslation, Trans } from 'react-i18next';
import { motion } from 'motion/react';
import { Target, Eye, Heart, Users } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import GradientHighlighter from '@/components/gradient-highlighter';

const CompanyStory = () => {
  const { t } = useTranslation('about');

  const values = [
    {
      key: 'innovation',
      icon: Target,
      color: 'text-blue-600 dark:text-blue-400',
      bgColor: 'bg-blue-100 dark:bg-blue-900/30',
    },
    {
      key: 'reliability',
      icon: Eye,
      color: 'text-cyan-600 dark:text-cyan-400',
      bgColor: 'bg-cyan-100 dark:bg-cyan-900/30',
    },
    {
      key: 'excellence',
      icon: Heart,
      color: 'text-primary dark:text-primary',
      bgColor: 'bg-primary/10 dark:bg-primary/20',
    },
    {
      key: 'partnership',
      icon: Users,
      color: 'text-secondary dark:text-secondary',
      bgColor: 'bg-secondary/10 dark:bg-secondary/20',
    },
  ];

  return (
    <section className="from-background to-muted/30 bg-gradient-to-b px-4 py-20">
      <div className="mx-auto max-w-7xl">
        {/* Section header */}
        <div className="mb-16 text-center">
          <Trans
            i18nKey="story.title"
            ns="about"
            parent={motion.h2}
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true, margin: '-100px' }}
            transition={{ duration: 0.8 }}
            className="mb-4 text-3xl font-bold text-gray-900 text-shadow-2xs sm:text-4xl md:text-5xl dark:text-white"
            components={{
              highlighted: <GradientHighlighter />,
            }}
          />
          <motion.p
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true, margin: '-100px' }}
            transition={{ duration: 0.8, delay: 0.2 }}
            className="text-primary text-xl font-semibold text-shadow-xs"
          >
            {t('story.subtitle')}
          </motion.p>
        </div>

        {/* Main story content */}
        <div className="mb-20 grid items-center gap-12 lg:grid-cols-2 lg:gap-16">
          {/* Story text */}
          <motion.div
            initial={{ opacity: 0, x: -50 }}
            whileInView={{ opacity: 1, x: 0 }}
            viewport={{ once: true, margin: '-100px' }}
            transition={{ duration: 0.8 }}
            className="space-y-6"
          >
            <p className="text-lg leading-relaxed text-gray-700 dark:text-gray-300">
              {t('story.description')}
            </p>
          </motion.div>

          {/* Visual element */}
          <motion.div
            initial={{ opacity: 0, x: 50 }}
            whileInView={{ opacity: 1, x: 0 }}
            viewport={{ once: true, margin: '-100px' }}
            transition={{ duration: 0.8, delay: 0.2 }}
            className="relative"
          >
            <div className="from-primary/20 to-secondary/20 border-primary/20 relative h-80 rounded-2xl border bg-gradient-to-br p-8 backdrop-blur-sm">
              <div className="absolute inset-0 rounded-2xl bg-gradient-to-br from-blue-500/10 to-cyan-500/10" />
              <div className="relative z-10 flex h-full items-center justify-center">
                <div className="text-center">
                  <motion.div
                    animate={{
                      scale: [1, 1.1, 1],
                      rotate: [0, 5, 0],
                    }}
                    transition={{
                      duration: 4,
                      repeat: Infinity,
                      ease: 'easeInOut',
                    }}
                    className="from-primary to-secondary mx-auto mb-4 flex h-20 w-20 items-center justify-center rounded-full bg-gradient-to-br"
                  >
                    <Target className="h-10 w-10 text-white" />
                  </motion.div>
                  <h3 className="mb-2 text-2xl font-bold text-gray-900 text-shadow-xs dark:text-white">
                    Innovation Driven
                  </h3>
                  <p className="text-gray-600 dark:text-gray-400">
                    Transforming document processing since day one
                  </p>
                </div>
              </div>
            </div>
          </motion.div>
        </div>

        {/* Mission and Vision */}
        <div className="mb-20 grid gap-8 md:grid-cols-2">
          {/* Mission */}
          <motion.div
            initial={{ opacity: 0, y: 50 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true, margin: '-100px' }}
            transition={{ duration: 0.8 }}
          >
            <Card className="border-primary/20 from-background to-primary/5 h-full bg-gradient-to-br transition-all duration-300 hover:shadow-lg">
              <CardContent className="p-8">
                <h3 className="mb-4 text-2xl font-bold text-gray-900 text-shadow-xs dark:text-white">
                  {t('story.mission.title')}
                </h3>
                <p className="leading-relaxed text-gray-700 dark:text-gray-300">
                  {t('story.mission.description')}
                </p>
              </CardContent>
            </Card>
          </motion.div>

          {/* Vision */}
          <motion.div
            initial={{ opacity: 0, y: 50 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true, margin: '-100px' }}
            transition={{ duration: 0.8, delay: 0.2 }}
          >
            <Card className="border-secondary/20 from-background to-secondary/5 h-full bg-gradient-to-br transition-all duration-300 hover:shadow-lg">
              <CardContent className="p-8">
                <h3 className="mb-4 text-2xl font-bold text-gray-900 text-shadow-xs dark:text-white">
                  {t('story.vision.title')}
                </h3>
                <p className="leading-relaxed text-gray-700 dark:text-gray-300">
                  {t('story.vision.description')}
                </p>
              </CardContent>
            </Card>
          </motion.div>
        </div>

        {/* Values section */}
        <div className="mb-12 text-center">
          <motion.h3
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true, margin: '-100px' }}
            transition={{ duration: 0.8 }}
            className="mb-4 text-3xl font-bold text-gray-900 text-shadow-xs dark:text-white"
          >
            {t('story.values.title')}
          </motion.h3>
        </div>

        {/* Values grid */}
        <div className="grid gap-6 sm:grid-cols-2 lg:grid-cols-4">
          {values.map((value, index) => {
            const IconComponent = value.icon;
            return (
              <motion.div
                key={value.key}
                initial={{ opacity: 0, y: 50 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true, margin: '-100px' }}
                transition={{ duration: 0.8, delay: index * 0.1 }}
                whileHover={{ y: -5, scale: 1.02 }}
                className="group"
              >
                <Card className="border-border/50 bg-card/80 group-hover:border-primary/30 h-full backdrop-blur-sm transition-all duration-300 hover:shadow-xl">
                  <CardContent className="p-6 text-center">
                    <div
                      className={`mx-auto mb-4 h-16 w-16 rounded-full ${value.bgColor} flex items-center justify-center transition-all duration-300 group-hover:scale-110`}
                    >
                      <IconComponent className={`h-8 w-8 ${value.color}`} />
                    </div>
                    <h4 className="mb-3 text-xl font-semibold text-gray-900 dark:text-white">
                      {t(`story.values.items.${value.key}.title`)}
                    </h4>
                    <p className="leading-relaxed text-gray-600 dark:text-gray-400">
                      {t(`story.values.items.${value.key}.description`)}
                    </p>
                  </CardContent>
                </Card>
              </motion.div>
            );
          })}
        </div>
      </div>
    </section>
  );
};

export default CompanyStory;
