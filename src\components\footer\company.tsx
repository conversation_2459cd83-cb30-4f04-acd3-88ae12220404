import React from 'react';
import { motion } from 'motion/react';
import { itemVariants } from '@/constants/footer';
import Brand from '@/components/brand';

const Company = () => {
  return (
    <motion.div className="space-y-6" variants={itemVariants}>
      <motion.div
        whileHover={{ scale: 1.05 }}
        transition={{ type: 'spring', stiffness: 300, damping: 20 }}
      >
        <Brand />
      </motion.div>

      <motion.p
        className="text-muted-foreground text-sm leading-relaxed"
        variants={itemVariants}
      >
        Centris, the leading-edge electronic document processing system,
        transforms the way you manage and organize all kinds of your documents.
        Say goodbye to manual processing and data extraction.
      </motion.p>

      {/* Contact Info */}
      <motion.div className="space-y-2 text-sm" variants={itemVariants}>
        <div className="text-foreground font-medium">
          ISS Europe, spol. s r.o.
        </div>
        <div className="text-muted-foreground">Belgradská 1267/581</div>
        <div className="text-muted-foreground">120 00 Praha 2</div>
        <div className="text-muted-foreground">IČO: 63672804</div>
        <div className="text-muted-foreground">DIČ: CZ63672804</div>
      </motion.div>
    </motion.div>
  );
};

export default Company;
