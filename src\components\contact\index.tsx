'use client';

import React from 'react';
import { motion } from 'motion/react';
import { useTranslation } from 'react-i18next';
import { Phone, MapPin, Building2 } from 'lucide-react';
import { Button } from '@/components/ui/button';
import ContactForm from './form';

const Contact = () => {
  const { t } = useTranslation('contact');

  const handleCallClick = () => {
    window.open('tel:+420222512576', '_self');
  };

  return (
    <div className="from-background via-muted/30 to-background dark:from-background dark:via-muted/20 dark:to-card min-h-screen bg-gradient-to-br">
      {/* Background decorative elements */}
      <div className="absolute inset-0 overflow-hidden">
        <motion.div
          className="from-primary/20 to-secondary/20 absolute -top-40 -right-40 h-80 w-80 rounded-full bg-gradient-to-br blur-3xl"
          animate={{
            scale: [1, 1.2, 1],
            rotate: [0, 180, 360],
          }}
          transition={{
            duration: 20,
            repeat: Infinity,
            ease: 'linear',
          }}
        />
        <motion.div
          className="from-accent/20 to-primary/20 absolute -bottom-40 -left-40 h-80 w-80 rounded-full bg-gradient-to-br blur-3xl"
          animate={{
            scale: [1.2, 1, 1.2],
            rotate: [360, 180, 0],
          }}
          transition={{
            duration: 25,
            repeat: Infinity,
            ease: 'linear',
          }}
        />
      </div>

      {/* Hero Section */}
      <section className="relative z-10 overflow-hidden px-4 py-16 sm:px-6 lg:px-8">
        <div className="mx-auto max-w-7xl">
          <div className="grid gap-12 lg:grid-cols-2 lg:gap-16">
            {/* Left Column - Hero Content */}
            <motion.div
              initial={{ opacity: 0, x: -50 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8 }}
              className="flex flex-col justify-center"
            >
              <h1 className="text-foreground mb-6 text-4xl font-bold tracking-tight sm:text-5xl lg:text-6xl">
                {t('hero.title')}
              </h1>

              <p className="text-muted-foreground mb-8 text-lg sm:text-xl">
                {t('hero.description')}
              </p>

              {/* Headquarters Info */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 0.2 }}
                whileHover={{
                  y: -5,
                  rotateX: 5,
                  rotateY: 5,
                  scale: 1.02,
                }}
                className="card-modern perspective-1000 transform-gpu p-6 will-change-transform"
                style={{
                  transformStyle: 'preserve-3d',
                }}
              >
                <div className="mb-4 flex items-center gap-3">
                  <Building2 className="text-primary h-6 w-6" />
                  <h3 className="text-foreground text-xl font-semibold">
                    {t('hero.headquarters.title')}
                  </h3>
                </div>

                <div className="text-muted-foreground space-y-2">
                  <div className="flex items-start gap-3">
                    <MapPin className="text-primary mt-1 h-4 w-4" />
                    <div>
                      <p>{t('hero.headquarters.address.street')}</p>
                      <p>{t('hero.headquarters.address.district')}</p>
                      <p>{t('hero.headquarters.address.postal')}</p>
                      <p>{t('hero.headquarters.address.country')}</p>
                    </div>
                  </div>

                  <div className="flex items-center gap-3">
                    <Phone className="text-primary h-4 w-4" />
                    <p>{t('hero.headquarters.address.phone')}</p>
                  </div>
                </div>

                {/* Call to Action Button */}
                <motion.div
                  className="mt-6"
                  whileHover={{
                    scale: 1.05,
                    rotateX: 10,
                    z: 50,
                  }}
                  whileTap={{ scale: 0.95 }}
                  transition={{ type: 'spring', stiffness: 300, damping: 20 }}
                >
                  <Button
                    onClick={handleCallClick}
                    className="from-primary to-secondary text-primary-foreground w-full bg-gradient-to-r shadow-lg transition-all duration-200 hover:shadow-xl"
                    aria-label={t('hero.cta.call_aria')}
                  >
                    <Phone className="mr-2 h-4 w-4" />
                    {t('hero.cta.call')}
                  </Button>
                </motion.div>
              </motion.div>
            </motion.div>

            {/* Right Column - Contact Form */}
            <ContactForm />
          </div>
        </div>
      </section>
    </div>
  );
};

export default Contact;
