import { NextRequest, NextResponse } from 'next/server';
import { detectLanguageFromRequest } from '@/lib/i18n/detector';
import { cookieName, headerName } from '@/lib/i18n/settings';
import debug from 'debug';

const langLog = debug('middleware:lang');

/**
 * Middleware for language detection WITHOUT URL redirects
 * Only sets headers for server components, URL remains clean
 */
export async function middleware(request: NextRequest) {
  // Skip middleware for static files and API routes
  if (
    request.nextUrl.pathname.startsWith('/_next') ||
    request.nextUrl.pathname.startsWith('/api') ||
    request.nextUrl.pathname.includes('.')
  ) {
    return NextResponse.next();
  }

  // Detect language from request (cookie → Accept-Language → fallback)
  const detectedLanguage = await detectLanguageFromRequest(request);

  langLog(
    `🎯 Middleware detected language: ${detectedLanguage} for URL: ${request.nextUrl.pathname}`
  );

  // Create a response with added headers (NO redirect!)
  const response = NextResponse.next();

  // Add language info to headers for server components
  response.headers.set(headerName, detectedLanguage);
  response.headers.set('x-pathname', request.nextUrl.pathname);

  // SEO headers
  response.headers.set('Content-Language', detectedLanguage);
  response.headers.set('Vary', 'Accept-Language, Cookie');

  // Make sure that the cookie is set for consistency
  if (!request.cookies.get(cookieName)) {
    response.cookies.set(cookieName, detectedLanguage, {
      httpOnly: false, // Must be available for JavaScript
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'lax',
      maxAge: 60 * 60 * 24 * 365, // 1 rok
      path: '/',
    });
  }

  return response;
}

export const config = {
  matcher: [
    /*
     * Match all paths except:
     * - API routes (/api/*)
     * - Next.js internals (_next/*)
     * - Static files (containing a dot)
     */
    '/((?!api|_next/static|_next/image|favicon.ico|robots.txt|sitemap.xml).*)',
  ],
};
