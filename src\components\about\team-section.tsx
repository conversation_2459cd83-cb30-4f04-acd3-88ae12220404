'use client';

import React from 'react';
import { useTranslation, Trans } from 'react-i18next';
import { motion } from 'motion/react';
import {
  Users,
  Lightbulb,
  Target,
  Heart,
  Award,
  Code,
  Settings,
  Shield,
  Headphones,
  TrendingUp,
} from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import GradientHighlighter from '@/components/gradient-highlighter';

const TeamSection = () => {
  const { t } = useTranslation('about');

  const cultureHighlights = [
    {
      icon: Users,
      color: 'text-blue-600 dark:text-blue-400',
      bgColor: 'bg-blue-100 dark:bg-blue-900/30',
    },
    {
      icon: Lightbulb,
      color: 'text-yellow-600 dark:text-yellow-400',
      bgColor: 'bg-yellow-100 dark:bg-yellow-900/30',
    },
    {
      icon: Target,
      color: 'text-green-600 dark:text-green-400',
      bgColor: 'bg-green-100 dark:bg-green-900/30',
    },
    {
      icon: Heart,
      color: 'text-red-600 dark:text-red-400',
      bgColor: 'bg-red-100 dark:bg-red-900/30',
    },
    {
      icon: Award,
      color: 'text-purple-600 dark:text-purple-400',
      bgColor: 'bg-purple-100 dark:bg-purple-900/30',
    },
  ];

  const expertiseAreas = [
    {
      icon: Code,
      color: 'text-blue-600 dark:text-blue-400',
      bgColor: 'bg-blue-100 dark:bg-blue-900/30',
    },
    {
      icon: Settings,
      color: 'text-cyan-600 dark:text-cyan-400',
      bgColor: 'bg-cyan-100 dark:bg-cyan-900/30',
    },
    {
      icon: TrendingUp,
      color: 'text-green-600 dark:text-green-400',
      bgColor: 'bg-green-100 dark:bg-green-900/30',
    },
    {
      icon: Shield,
      color: 'text-red-600 dark:text-red-400',
      bgColor: 'bg-red-100 dark:bg-red-900/30',
    },
    {
      icon: Headphones,
      color: 'text-purple-600 dark:text-purple-400',
      bgColor: 'bg-purple-100 dark:bg-purple-900/30',
    },
  ];

  return (
    <section className="from-background to-muted/30 bg-gradient-to-b px-4 py-20">
      <div className="mx-auto max-w-7xl">
        {/* Section header */}
        <div className="mb-16 text-center">
          <Trans
            i18nKey="team.title"
            ns="about"
            parent={motion.h2}
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true, margin: '-100px' }}
            transition={{ duration: 0.8 }}
            className="mb-4 text-3xl font-bold text-gray-900 sm:text-4xl md:text-5xl dark:text-white"
            components={{
              highlighted: <GradientHighlighter />,
            }}
          />
          <motion.p
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true, margin: '-100px' }}
            transition={{ duration: 0.8, delay: 0.2 }}
            className="text-primary mb-4 text-xl font-semibold"
          >
            {t('team.subtitle')}
          </motion.p>
          <motion.p
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true, margin: '-100px' }}
            transition={{ duration: 0.8, delay: 0.4 }}
            className="mx-auto max-w-3xl text-lg text-gray-700 dark:text-gray-300"
          >
            {t('team.description')}
          </motion.p>
        </div>

        {/* Main content grid */}
        <div className="mb-20 grid items-start gap-12 lg:grid-cols-2 lg:gap-16">
          {/* Company Culture */}
          <motion.div
            initial={{ opacity: 0, x: -50 }}
            whileInView={{ opacity: 1, x: 0 }}
            viewport={{ once: true, margin: '-100px' }}
            transition={{ duration: 0.8 }}
          >
            <Card className="border-border/50 bg-card/80 h-full backdrop-blur-sm transition-all duration-300 hover:shadow-xl">
              <CardContent className="p-8">
                <h3 className="mb-4 text-2xl font-bold text-gray-900 dark:text-white">
                  {t('team.culture.title')}
                </h3>
                <p className="mb-6 leading-relaxed text-gray-700 dark:text-gray-300">
                  {t('team.culture.description')}
                </p>

                {/* Culture highlights */}
                <div className="space-y-4">
                  {(
                    t('team.culture.highlights', {
                      returnObjects: true,
                    }) as string[]
                  ).map((highlight, index) => {
                    const IconComponent =
                      cultureHighlights[index]?.icon || Users;
                    const iconColor =
                      cultureHighlights[index]?.color ||
                      'text-blue-600 dark:text-blue-400';
                    const iconBg =
                      cultureHighlights[index]?.bgColor ||
                      'bg-blue-100 dark:bg-blue-900/30';

                    return (
                      <motion.div
                        key={index}
                        initial={{ opacity: 0, x: -20 }}
                        whileInView={{ opacity: 1, x: 0 }}
                        viewport={{ once: true }}
                        transition={{ duration: 0.5, delay: index * 0.1 }}
                        className="group flex items-center gap-4"
                      >
                        <div
                          className={`h-10 w-10 rounded-lg ${iconBg} flex items-center justify-center transition-all duration-300 group-hover:scale-110`}
                        >
                          <IconComponent className={`h-5 w-5 ${iconColor}`} />
                        </div>
                        <span className="font-medium text-gray-700 dark:text-gray-300">
                          {highlight}
                        </span>
                      </motion.div>
                    );
                  })}
                </div>
              </CardContent>
            </Card>
          </motion.div>

          {/* Team Expertise */}
          <motion.div
            initial={{ opacity: 0, x: 50 }}
            whileInView={{ opacity: 1, x: 0 }}
            viewport={{ once: true, margin: '-100px' }}
            transition={{ duration: 0.8, delay: 0.2 }}
          >
            <Card className="border-border/50 bg-card/80 h-full backdrop-blur-sm transition-all duration-300 hover:shadow-xl">
              <CardContent className="p-8">
                <h3 className="mb-4 text-2xl font-bold text-gray-900 dark:text-white">
                  {t('team.expertise.title')}
                </h3>

                {/* Expertise areas */}
                <div className="space-y-4">
                  {(
                    t('team.expertise.areas', {
                      returnObjects: true,
                    }) as string[]
                  ).map((area, index) => {
                    const IconComponent = expertiseAreas[index]?.icon || Code;
                    const iconColor =
                      expertiseAreas[index]?.color ||
                      'text-blue-600 dark:text-blue-400';
                    const iconBg =
                      expertiseAreas[index]?.bgColor ||
                      'bg-blue-100 dark:bg-blue-900/30';

                    return (
                      <motion.div
                        key={index}
                        initial={{ opacity: 0, x: 20 }}
                        whileInView={{ opacity: 1, x: 0 }}
                        viewport={{ once: true }}
                        transition={{ duration: 0.5, delay: index * 0.1 }}
                        className="group flex items-center gap-4"
                      >
                        <div
                          className={`h-10 w-10 rounded-lg ${iconBg} flex items-center justify-center transition-all duration-300 group-hover:scale-110`}
                        >
                          <IconComponent className={`h-5 w-5 ${iconColor}`} />
                        </div>
                        <span className="font-medium text-gray-700 dark:text-gray-300">
                          {area}
                        </span>
                      </motion.div>
                    );
                  })}
                </div>
              </CardContent>
            </Card>
          </motion.div>
        </div>

        {/* Team showcase section */}
        <motion.div
          initial={{ opacity: 0, y: 50 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true, margin: '-100px' }}
          transition={{ duration: 0.8, delay: 0.4 }}
          className="text-center"
        >
          <Card className="border-primary/20 from-primary/5 to-secondary/5 overflow-hidden bg-gradient-to-br">
            <CardContent className="p-8">
              <h3 className="mb-6 text-2xl font-bold text-gray-900 dark:text-white">
                Join Our Growing Team
              </h3>
              <p className="mx-auto mb-8 max-w-2xl text-gray-700 dark:text-gray-300">
                We&apos;re always looking for talented individuals who share our
                passion for innovation and excellence in document processing
                technology.
              </p>

              {/* Team benefits */}
              <div className="mb-8 grid gap-4 sm:grid-cols-2 lg:grid-cols-4">
                <motion.div
                  whileHover={{ scale: 1.05 }}
                  className="bg-background/50 border-border/50 rounded-lg border p-4"
                >
                  <div className="mx-auto mb-3 flex h-12 w-12 items-center justify-center rounded-full bg-blue-100 dark:bg-blue-900/30">
                    <Lightbulb className="h-6 w-6 text-blue-600 dark:text-blue-400" />
                  </div>
                  <h4 className="mb-2 font-semibold text-gray-900 dark:text-white">
                    Innovation
                  </h4>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    Work on cutting-edge technology
                  </p>
                </motion.div>

                <motion.div
                  whileHover={{ scale: 1.05 }}
                  className="bg-background/50 border-border/50 rounded-lg border p-4"
                >
                  <div className="mx-auto mb-3 flex h-12 w-12 items-center justify-center rounded-full bg-green-100 dark:bg-green-900/30">
                    <TrendingUp className="h-6 w-6 text-green-600 dark:text-green-400" />
                  </div>
                  <h4 className="mb-2 font-semibold text-gray-900 dark:text-white">
                    Growth
                  </h4>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    Continuous learning opportunities
                  </p>
                </motion.div>

                <motion.div
                  whileHover={{ scale: 1.05 }}
                  className="bg-background/50 border-border/50 rounded-lg border p-4"
                >
                  <div className="mx-auto mb-3 flex h-12 w-12 items-center justify-center rounded-full bg-purple-100 dark:bg-purple-900/30">
                    <Users className="h-6 w-6 text-purple-600 dark:text-purple-400" />
                  </div>
                  <h4 className="mb-2 font-semibold text-gray-900 dark:text-white">
                    Collaboration
                  </h4>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    Work with talented professionals
                  </p>
                </motion.div>

                <motion.div
                  whileHover={{ scale: 1.05 }}
                  className="bg-background/50 border-border/50 rounded-lg border p-4"
                >
                  <div className="mx-auto mb-3 flex h-12 w-12 items-center justify-center rounded-full bg-orange-100 dark:bg-orange-900/30">
                    <Award className="h-6 w-6 text-orange-600 dark:text-orange-400" />
                  </div>
                  <h4 className="mb-2 font-semibold text-gray-900 dark:text-white">
                    Recognition
                  </h4>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    Your contributions matter
                  </p>
                </motion.div>
              </div>

              {/* Call to action */}
              <div className="flex flex-wrap justify-center gap-4">
                <Badge
                  variant="outline"
                  className="text-primary border-primary/30 bg-primary/10"
                >
                  Remote Friendly
                </Badge>
                <Badge
                  variant="outline"
                  className="text-secondary border-secondary/30 bg-secondary/10"
                >
                  Competitive Benefits
                </Badge>
                <Badge
                  variant="outline"
                  className="border-green-600/30 bg-green-600/10 text-green-600"
                >
                  Career Development
                </Badge>
              </div>
            </CardContent>
          </Card>
        </motion.div>
      </div>
    </section>
  );
};

export default TeamSection;
