'use client';

import i18next from 'i18next';
import { initReactI18next } from 'react-i18next/initReactI18next';
import LanguageDetector from 'i18next-browser-languagedetector';
import resourcesToBackend from 'i18next-resources-to-backend';
import { getI18nextOptions, cookieName, type Language } from './settings';
import debug from 'debug';

const langLog = debug('i18n:client:lang');

// Flag for preventing reinitialization
let isClientInitialized = false;

/**
 * Client-side i18next initialization according to official documentation
 * Uses singleton pattern recommended for browser environment
 */
export async function initClientI18n(
  initialLanguage?: Language
): Promise<typeof i18next> {
  if (isClientInitialized) {
    return i18next;
  }

  langLog('🚀 Initializing client-side i18next');

  await i18next
    .use(initReactI18next)
    .use(LanguageDetector)
    .use(
      resourcesToBackend((language: string, namespace: string) => {
        // Async loading of translations on the client side
        return import(`../../locales/${language}/${namespace}.json`).catch(
          (error) => {
            console.error(
              `Failed to load client translation: ${language}/${namespace}.json`,
              error
            );
            return {};
          }
        );
      })
    )
    .init({
      ...getI18nextOptions(initialLanguage),

      // Client-specific settings
      detection: {
        // Detection order on the client side
        order: ['cookie', 'localStorage', 'navigator', 'htmlTag'],

        // Cache preferences in cookies and localStorage
        caches: ['cookie', 'localStorage'],

        // Cookie configuration
        cookieOptions: {
          path: '/',
          sameSite: 'lax',
          secure: process.env.NODE_ENV === 'production',
        },

        // Overwriting the cookie name
        lookupCookie: cookieName,
        lookupLocalStorage: cookieName,
      },

      // Lazy loading for performance
      initImmediate: false,

      // React optimization
      react: {
        useSuspense: false, // Prevention of hydration problems
      },
    });

  isClientInitialized = true;
  return i18next;
}

/**
 * Change language with automatic persistence
 * Combines i18next changeLanguage with custom API call
 */
export async function changeClientLanguage(
  newLanguage: Language
): Promise<void> {
  if (!isClientInitialized) {
    console.warn('i18next not initialized, cannot change language');
    return;
  }

  try {
    langLog(`🔄 Changing language to: ${newLanguage}`);

    // 1. Change the language in i18next (automatically updates the cache)
    await i18next.changeLanguage(newLanguage);

    // 2. Synchronize with the server via API
    await fetch('/api/locale', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ language: newLanguage }),
    });

    langLog(`✅ Language changed to: ${newLanguage}`);
  } catch (error) {
    console.error('Failed to change language:', error);
    throw error;
  }
}

/**
 * Get current client language
 * Fallback safe function for component use
 */
export function getCurrentClientLanguage(): Language {
  if (!isClientInitialized || !i18next.isInitialized) {
    return 'en'; // Safe fallback
  }

  return i18next.language as Language;
}
