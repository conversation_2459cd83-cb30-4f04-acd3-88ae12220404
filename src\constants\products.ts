import {
  FileText,
  FileUp,
  FileCheck,
  ShoppingCart,
  GitBranch,
  Package,
  File,
  Scale,
  Mail,
  Search,
  Archive,
  Users,
} from 'lucide-react';

export const productData = [
  {
    id: 'received-invoices',
    icon: FileText,
    gradient: 'from-blue-600 to-sky-500',
    delay: 0.1,
  },
  {
    id: 'issued-invoices',
    icon: FileUp,
    gradient: 'from-sky-500 to-cyan-400',
    delay: 0.2,
  },
  {
    id: 'contracts',
    icon: FileCheck,
    gradient: 'from-cyan-400 to-blue-600',
    delay: 0.3,
  },
  {
    id: 'requisitions',
    icon: ShoppingCart,
    gradient: 'from-blue-600 to-sky-500',
    delay: 0.4,
  },
  {
    id: 'directives',
    icon: GitBranch,
    gradient: 'from-sky-500 to-cyan-400',
    delay: 0.5,
  },
  {
    id: 'orders',
    icon: Package,
    gradient: 'from-cyan-400 to-blue-600',
    delay: 0.6,
  },
  {
    id: 'documents',
    icon: File,
    gradient: 'from-blue-600 to-sky-500',
    delay: 0.7,
  },
  {
    id: 'legal-documents',
    icon: Scale,
    gradient: 'from-sky-500 to-cyan-400',
    delay: 0.8,
  },
  {
    id: 'mailroom',
    icon: Mail,
    gradient: 'from-cyan-400 to-blue-600',
    delay: 0.9,
  },
  {
    id: 'data-mining',
    icon: Search,
    gradient: 'from-blue-600 to-sky-500',
    delay: 1.0,
  },
  {
    id: 'digital-archive',
    icon: Archive,
    gradient: 'from-sky-500 to-cyan-400',
    delay: 1.1,
  },
  {
    id: 'user-management',
    icon: Users,
    gradient: 'from-cyan-400 to-blue-600',
    delay: 1.2,
  },
];
