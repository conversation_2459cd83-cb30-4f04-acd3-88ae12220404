'use client';

import { useState, useEffect } from 'react';
import { motion } from 'motion/react';
import { useTranslation, Trans } from 'react-i18next';
import {
  Shield,
  Database,
  Archive,
  FileCheck,
  Building2,
  Workflow,
} from 'lucide-react';
import GradientText from '@/components/gradient-text';

const DataControl = () => {
  const { t } = useTranslation('common');
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  if (!mounted) return null;

  // Floating animation variants
  const floatAnimation = {
    y: [-10, 10, -10],
    rotateZ: [-5, 5, -5],
    scale: [1, 1.05, 1],
  };

  const floatingElements = Array.from({ length: 15 }, (_, i) => ({
    id: i,
    delay: i * 0.3,
    duration: 4 + (i % 3),
    size: Math.random() * 8 + 4,
    opacity: Math.random() * 0.4 + 0.1,
  }));

  return (
    <section className="relative overflow-hidden py-20 lg:py-32">
      {/* Gradient Background */}
      <div className="from-background via-muted/20 to-background absolute inset-0 bg-gradient-to-br">
        {/* Light theme gradient overlay */}
        <div className="from-primary/5 via-secondary/10 to-accent/5 dark:from-primary/10 dark:via-secondary/15 dark:to-accent/10 absolute inset-0 bg-gradient-to-r" />

        {/* Radial gradients for depth */}
        <div className="bg-gradient-radial from-primary/20 dark:from-primary/30 absolute top-0 left-1/4 h-96 w-96 rounded-full to-transparent blur-3xl" />
        <div className="bg-gradient-radial from-accent/15 dark:from-accent/25 absolute right-1/4 bottom-0 h-80 w-80 rounded-full to-transparent blur-3xl" />
      </div>

      {/* Floating Background Elements */}
      <div className="pointer-events-none absolute inset-0">
        {floatingElements.map((element) => (
          <motion.div
            key={element.id}
            className="from-primary/30 to-secondary/20 dark:from-primary/40 dark:to-secondary/30 absolute rounded-full bg-gradient-to-br blur-sm"
            style={{
              width: element.size,
              height: element.size,
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
              opacity: element.opacity,
            }}
            animate={floatAnimation}
            transition={{
              duration: element.duration,
              delay: element.delay,
              repeat: Infinity,
              ease: 'easeInOut',
            }}
          />
        ))}
      </div>

      <div className="relative z-10 container mx-auto px-4">
        <div className="grid items-center gap-16 lg:grid-cols-2">
          {/* Left Content */}
          <motion.div
            initial={{ opacity: 0, x: -30 }}
            whileInView={{ opacity: 1, x: 0 }}
            viewport={{ once: true, margin: '-100px' }}
            transition={{ duration: 0.8 }}
            className="space-y-8"
          >
            <Trans
              i18nKey="data_control.title"
              ns="common"
              parent={motion.h2}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ delay: 0.2, duration: 0.6 }}
              className="text-foreground text-4xl leading-tight font-bold text-shadow-2xs lg:text-5xl"
              components={{
                highlighted: <GradientText />,
                br: <br />,
              }}
            />

            <div className="text-muted-foreground space-y-6 text-lg leading-relaxed text-shadow-xs">
              <motion.p
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ delay: 0.3, duration: 0.6 }}
              >
                {t('data_control.description.p1')}
              </motion.p>

              <motion.p
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ delay: 0.4, duration: 0.6 }}
              >
                {t('data_control.description.p2')}
              </motion.p>

              <motion.p
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ delay: 0.5, duration: 0.6 }}
              >
                {t('data_control.description.p3')}
              </motion.p>
            </div>
          </motion.div>

          {/* Right 3D Animation Area */}
          <motion.div
            initial={{ opacity: 0, x: 30 }}
            whileInView={{ opacity: 1, x: 0 }}
            viewport={{ once: true, margin: '-100px' }}
            transition={{ duration: 0.8, delay: 0.2 }}
            className="relative"
          >
            <div className="perspective-1000 preserve-3d relative h-96">
              {/* Central Document Hub */}
              <motion.div
                animate={{
                  rotateY: [0, 360],
                  rotateX: [0, 10, 0],
                }}
                transition={{
                  duration: 15,
                  repeat: Infinity,
                  ease: 'linear',
                }}
                className="absolute top-1/2 left-1/2 h-32 w-32 -translate-x-1/2 -translate-y-1/2 transform-gpu"
              >
                <div className="from-card/80 to-background/80 dark:from-card dark:to-background border-border flex h-full w-full items-center justify-center rounded-2xl border bg-gradient-to-br shadow-xl backdrop-blur-sm">
                  <Database className="text-primary h-12 w-12" />
                </div>
              </motion.div>

              {/* Orbiting Feature Icons */}
              {[
                {
                  icon: Shield,
                  angle: 0,
                  radius: 120,
                  color: 'text-primary',
                  delay: 0,
                },
                {
                  icon: Archive,
                  angle: 120,
                  radius: 100,
                  color: 'text-secondary',
                  delay: 0.5,
                },
                {
                  icon: FileCheck,
                  angle: 240,
                  radius: 110,
                  color: 'text-accent',
                  delay: 1,
                },
                {
                  icon: Workflow,
                  angle: 60,
                  radius: 140,
                  color: 'text-primary',
                  delay: 1.5,
                },
                {
                  icon: Building2,
                  angle: 180,
                  radius: 130,
                  color: 'text-secondary',
                  delay: 2,
                },
              ].map((item, index) => {
                const x = Math.cos((item.angle * Math.PI) / 180) * item.radius;
                const y = Math.sin((item.angle * Math.PI) / 180) * item.radius;

                return (
                  <motion.div
                    key={index}
                    className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2"
                    animate={{
                      x: [x, x + 5, x],
                      y: [y, y - 5, y],
                      rotateZ: [0, 360],
                    }}
                    transition={{
                      duration: 6 + index,
                      repeat: Infinity,
                      ease: 'easeInOut',
                      delay: item.delay,
                    }}
                  >
                    <motion.div
                      whileHover={{ scale: 1.2, z: 10 }}
                      className="from-card/90 to-background/90 dark:from-card dark:to-background border-border flex h-16 w-16 transform-gpu cursor-pointer items-center justify-center rounded-xl border bg-gradient-to-br shadow-lg backdrop-blur-sm"
                    >
                      <item.icon className={`h-8 w-8 ${item.color}`} />
                    </motion.div>
                  </motion.div>
                );
              })}

              {/* Connecting Lines */}
              <svg className="pointer-events-none absolute inset-0 h-full w-full">
                {[0, 1, 2].map((index) => {
                  const angle = index * 120;
                  const radius = 100;
                  const x1 = 50;
                  const y1 = 50;
                  const x2 =
                    50 + Math.cos((angle * Math.PI) / 180) * radius * 0.3;
                  const y2 =
                    50 + Math.sin((angle * Math.PI) / 180) * radius * 0.3;

                  return (
                    <motion.line
                      key={index}
                      x1={`${x1}%`}
                      y1={`${y1}%`}
                      x2={`${x2}%`}
                      y2={`${y2}%`}
                      stroke="url(#lineGradient)"
                      strokeWidth="2"
                      strokeDasharray="4,4"
                      initial={{ pathLength: 0, opacity: 0 }}
                      animate={{
                        pathLength: [0, 1, 0],
                        opacity: [0, 0.4, 0],
                      }}
                      transition={{
                        duration: 4,
                        repeat: Infinity,
                        delay: index * 0.7,
                        ease: 'easeInOut',
                      }}
                    />
                  );
                })}
                <defs>
                  <linearGradient
                    id="lineGradient"
                    x1="0%"
                    y1="0%"
                    x2="100%"
                    y2="100%"
                  >
                    <stop
                      offset="0%"
                      stopColor="oklch(0.608 0.279 245.831)"
                      stopOpacity="0.8"
                    />
                    <stop
                      offset="50%"
                      stopColor="oklch(0.714 0.123 238.75)"
                      stopOpacity="0.6"
                    />
                    <stop
                      offset="100%"
                      stopColor="oklch(0.821 0.135 195.198)"
                      stopOpacity="0.4"
                    />
                  </linearGradient>
                </defs>
              </svg>

              {/* Floating Data Particles */}
              {Array.from({ length: 8 }, (_, i) => (
                <motion.div
                  key={i}
                  className="from-primary to-accent absolute h-2 w-2 rounded-full bg-gradient-to-r"
                  style={{
                    left: `${20 + i * 10}%`,
                    top: `${30 + (i % 3) * 20}%`,
                  }}
                  animate={{
                    y: [-20, 20, -20],
                    x: [-10, 10, -10],
                    opacity: [0.3, 0.8, 0.3],
                    scale: [1, 1.5, 1],
                  }}
                  transition={{
                    duration: 3 + (i % 2),
                    repeat: Infinity,
                    delay: i * 0.2,
                    ease: 'easeInOut',
                  }}
                />
              ))}

              {/* Glow Effect */}
              <div className="bg-gradient-radial from-primary/20 via-secondary/10 dark:from-primary/30 dark:via-secondary/20 pointer-events-none absolute top-1/2 left-1/2 h-64 w-64 -translate-x-1/2 -translate-y-1/2 rounded-full to-transparent blur-2xl" />
            </div>
          </motion.div>
        </div>
      </div>

      {/* Bottom gradient fade */}
      <div className="from-background/80 pointer-events-none absolute right-0 bottom-0 left-0 h-20 bg-gradient-to-t to-transparent" />
    </section>
  );
};

export default DataControl;
