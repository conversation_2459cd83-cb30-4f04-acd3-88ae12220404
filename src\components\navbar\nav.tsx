import React from 'react';
import { detectLanguageFromHeaders } from '@/lib/i18n/detector';
import { getServerTranslation } from '@/lib/i18n/server';
import NavItem from './nav-item';

const Nav = async () => {
  const language = await detectLanguageFromHeaders();
  const { t } = await getServerTranslation(language, 'common');

  return (
    <nav className="hidden items-center space-x-8 md:flex">
      <NavItem href="/products" name={t('navigation.products')} />
      <NavItem href="/careers" name={t('navigation.careers')} />
      <NavItem href="/about" name={t('navigation.about')} />
    </nav>
  );
};

export default Nav;
