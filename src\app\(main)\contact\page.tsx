import React from 'react';
import type { Metadata } from 'next';
import { detectLanguageFromHeaders } from '@/lib/i18n/detector';
import { getServerTranslation } from '@/lib/i18n/server';
import Contact from '@/components/contact';

export async function generateMetadata(): Promise<Metadata> {
  const language = await detectLanguageFromHeaders();
  const { t } = await getServerTranslation(language, 'contact');

  return {
    title: t('site.title'),
    description: t('site.description'),
  };
}

const ContactPage = () => {
  return <Contact />;
};

export default ContactPage;
