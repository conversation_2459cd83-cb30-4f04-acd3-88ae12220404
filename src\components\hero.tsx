'use client';

import React, { useEffect, useState } from 'react';
import { useTranslation, Trans } from 'react-i18next';
import { motion } from 'motion/react';
import { Award, ArrowRight, Play } from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import FloatingDocumentCard from '@/components/floating-doc-card';
import GradientHighlighter from '@/components/gradient-highlighter';
import FloatingLetter from '@/components/floating-letter';
import BackgroundLetter from '@/components/background-letter';
import FloatingNumber from '@/components/floating-number';
import {
  CARD_POSITIONS_MOBILE,
  CARD_POSITIONS_TABLET,
  CARD_POSITIONS_DESKTOP,
} from '@/constants/hero';

const Hero = () => {
  const { t } = useTranslation('common');

  const [dimensions, setDimensions] = useState({ width: 0, height: 0 });

  useEffect(() => {
    const updateDimensions = () => {
      setDimensions({ width: window.innerWidth, height: window.innerHeight });
    };

    updateDimensions();
    window.addEventListener('resize', updateDimensions);
    return () => window.removeEventListener('resize', updateDimensions);
  }, []);

  const isMobile = dimensions.width < 768;
  const isTablet = dimensions.width >= 768 && dimensions.width < 1024;

  const getCardPositions = () => {
    if (isMobile) {
      return CARD_POSITIONS_MOBILE;
    } else if (isTablet) {
      return CARD_POSITIONS_TABLET;
    } else {
      return CARD_POSITIONS_DESKTOP;
    }
  };

  const positions = getCardPositions();

  return (
    <section className="relative flex min-h-screen items-start justify-start overflow-hidden pt-22">
      {/* Background decorative elements */}
      <div className="pointer-events-none absolute inset-0">
        {/* Large gradient background orbs */}
        <motion.div
          initial={{ opacity: 0, scale: 0.8 }}
          animate={{ opacity: 0.3, scale: 1 }}
          transition={{ duration: 2 }}
          className="absolute -top-1/4 -left-1/4 h-1/2 w-1/2 rounded-full bg-gradient-to-br from-blue-500/40 to-cyan-500/30 blur-3xl dark:from-blue-500/20 dark:to-cyan-500/15"
        />
        <motion.div
          initial={{ opacity: 0, scale: 0.8 }}
          animate={{ opacity: 0.25, scale: 1 }}
          transition={{ duration: 2, delay: 0.5 }}
          className="absolute -right-1/4 -bottom-1/4 h-1/2 w-1/2 rounded-full bg-gradient-to-br from-indigo-500/30 to-purple-500/25 blur-3xl dark:from-indigo-500/15 dark:to-purple-500/10"
        />

        {/* Floating letters distributed across ENTIRE Hero area */}
        <FloatingLetter
          delay={0}
          letter="A"
          startX="5%"
          startY="10%"
          size="text-2xl"
          color="text-blue-600"
        />
        <FloatingLetter
          delay={1}
          letter="K"
          startX="88%"
          startY="8%"
          size="text-xl"
          color="text-sky-500"
        />
        <FloatingLetter
          delay={2}
          letter="W"
          startX="12%"
          startY="75%"
          size="text-3xl"
          color="text-cyan-400"
        />
        <FloatingLetter
          delay={3}
          letter="B"
          startX="92%"
          startY="72%"
          size="text-lg"
          color="text-blue-600"
        />
        <FloatingLetter
          delay={4}
          letter="Q"
          startX="55%"
          startY="35%"
          size="text-4xl"
          color="text-gray-600"
        />
        <FloatingLetter
          delay={5}
          letter="H"
          startX="85%"
          startY="38%"
          size="text-2xl"
          color="text-sky-500"
        />
        <FloatingLetter
          delay={6}
          letter="Z"
          startX="25%"
          startY="5%"
          size="text-3xl"
          color="text-cyan-400"
        />
        <FloatingLetter
          delay={7}
          letter="G"
          startX="90%"
          startY="85%"
          size="text-2xl"
          color="text-blue-600"
        />
        <FloatingLetter
          delay={8}
          letter="V"
          startX="18%"
          startY="90%"
          size="text-xl"
          color="text-gray-600"
        />
        <FloatingLetter
          delay={9}
          letter="J"
          startX="82%"
          startY="20%"
          size="text-3xl"
          color="text-sky-500"
        />
        <FloatingLetter
          delay={10}
          letter="P"
          startX="8%"
          startY="55%"
          size="text-xl"
          color="text-indigo-500"
        />
        <FloatingLetter
          delay={11}
          letter="T"
          startX="95%"
          startY="60%"
          size="text-2xl"
          color="text-purple-500"
        />
        <FloatingLetter
          delay={12}
          letter="F"
          startX="35%"
          startY="25%"
          size="text-2xl"
          color="text-blue-500"
        />
        <FloatingLetter
          delay={13}
          letter="C"
          startX="70%"
          startY="15%"
          size="text-xl"
          color="text-cyan-500"
        />
        <FloatingLetter
          delay={14}
          letter="D"
          startX="45%"
          startY="80%"
          size="text-3xl"
          color="text-gray-500"
        />

        {/* Background letters distributed across full width */}
        <BackgroundLetter
          letter="X"
          position="top-16 left-1/4"
          delay={0}
          size="text-6xl"
          color="text-gray-400/50"
        />
        <BackgroundLetter
          letter="Y"
          position="top-1/4 right-1/6"
          delay={2}
          size="text-5xl"
          color="text-blue-500/30"
        />
        <BackgroundLetter
          letter="M"
          position="bottom-1/4 left-1/3"
          delay={4}
          size="text-8xl"
          color="text-sky-400/40"
        />
        <BackgroundLetter
          letter="L"
          position="bottom-16 right-1/5"
          delay={6}
          size="text-4xl"
          color="text-cyan-300/50"
        />
        <BackgroundLetter
          letter="R"
          position="top-1/3 left-8"
          delay={8}
          size="text-9xl"
          color="text-gray-500/30"
        />
        <BackgroundLetter
          letter="N"
          position="bottom-1/3 right-8"
          delay={10}
          size="text-5xl"
          color="text-indigo-400/40"
        />
        <BackgroundLetter
          letter="S"
          position="top-2/3 right-1/4"
          delay={12}
          size="text-7xl"
          color="text-purple-400/35"
        />
        <BackgroundLetter
          letter="E"
          position="bottom-2/3 left-1/5"
          delay={14}
          size="text-6xl"
          color="text-blue-400/45"
        />

        {/* Numbers distributed across full area */}
        <FloatingNumber
          number="7"
          position="top-20 left-20"
          delay={1}
          size="text-6xl"
          color="text-cyan-400/70"
        />
        <FloatingNumber
          number="3"
          position="bottom-20 right-20"
          delay={3}
          size="text-5xl"
          color="text-blue-600/70"
        />
        <FloatingNumber
          number="9"
          position="top-1/3 right-1/4"
          delay={5}
          size="text-4xl"
          color="text-sky-500/60"
        />
        <FloatingNumber
          number="1"
          position="bottom-1/3 left-16"
          delay={7}
          size="text-7xl"
          color="text-gray-600/50"
        />
        <FloatingNumber
          number="5"
          position="top-2/3 right-1/6"
          delay={9}
          size="text-5xl"
          color="text-indigo-500/60"
        />
        <FloatingNumber
          number="2"
          position="bottom-2/3 left-1/3"
          delay={11}
          size="text-6xl"
          color="text-purple-500/55"
        />
        <FloatingNumber
          number="4"
          position="top-1/2 left-1/5"
          delay={13}
          size="text-4xl"
          color="text-cyan-500/65"
        />
        <FloatingNumber
          number="8"
          position="bottom-1/2 right-1/3"
          delay={15}
          size="text-5xl"
          color="text-blue-500/60"
        />

        {/* Enhanced grid overlay */}
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 0.15 }}
          transition={{ duration: 2, delay: 1 }}
          className="absolute inset-0 dark:opacity-8"
          style={{
            backgroundImage:
              'linear-gradient(rgba(59, 130, 246, 0.15) 1px, transparent 1px), linear-gradient(90deg, rgba(59, 130, 246, 0.15) 1px, transparent 1px)',
            backgroundSize: '60px 60px',
          }}
        />
      </div>

      {/* Overlay with better opacity */}
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 1.5, delay: 0.3 }}
        className="md:backdrop-blur-xxs absolute inset-0 z-10 bg-white/30 backdrop-blur-sm dark:bg-black/40"
      />

      {/* Main content - LEFT ALIGNED but with proper margin */}
      <div className="relative z-20 w-full pl-12 md:pl-20 lg:pl-24">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.5 }}
          className="max-w-2xl py-8"
        >
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.6, delay: 0.7 }}
          >
            <Badge className="mb-6 border-blue-200 bg-blue-500/20 px-6 py-3 font-semibold text-blue-700 backdrop-blur-sm transition-all duration-300 hover:bg-blue-500/30 dark:border-white/30 dark:bg-white/15 dark:text-blue-300">
              <Award className="mr-2 h-4 w-4" />
              Award-Winning Document Processing
            </Badge>
          </motion.div>

          <Trans
            i18nKey="hero.title"
            ns="common"
            parent={motion.h1}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.9 }}
            className="mb-6 text-4xl leading-tight font-bold text-gray-900 text-shadow-md sm:text-5xl md:text-6xl lg:text-7xl xl:text-6xl dark:text-white"
            components={{
              highlighted1: <GradientHighlighter />,
              highlighted2: <GradientHighlighter />,
              br: <br />,
            }}
          />

          <motion.p
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 1.1 }}
            className="mb-12 max-w-xl text-lg leading-relaxed font-medium text-gray-700 text-shadow-xs sm:text-xl md:text-2xl dark:text-gray-300"
          >
            {t('hero.description')}
          </motion.p>

          {/* Action Buttons */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 1.3 }}
            className="flex flex-col items-start gap-4 sm:flex-row"
          >
            <Button
              size="lg"
              className="group transform rounded-xl bg-gradient-to-r from-blue-600 to-cyan-600 px-8 py-4 font-semibold text-white shadow-lg transition-all duration-300 hover:scale-105 hover:from-blue-700 hover:to-cyan-700 hover:shadow-xl"
            >
              Get Started
              <ArrowRight className="ml-2 h-5 w-5 transition-transform group-hover:translate-x-1" />
            </Button>

            <Button
              size="lg"
              variant="outline"
              className="group rounded-xl border-2 border-gray-300 bg-white/80 px-8 py-4 font-semibold text-gray-700 shadow-lg backdrop-blur-sm transition-all duration-300 hover:border-blue-500 hover:bg-blue-50 hover:text-blue-700 hover:shadow-xl dark:border-gray-600 dark:bg-gray-800/80 dark:text-gray-200 dark:hover:border-blue-400 dark:hover:bg-gray-700/80 dark:hover:text-blue-300"
            >
              <Play className="mr-2 h-5 w-5 transition-transform group-hover:scale-110" />
              Request a Demo
            </Button>
          </motion.div>

          {/* Additional info or stats */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.8, delay: 1.5 }}
            className="mt-12 flex flex-wrap gap-8 text-sm text-gray-600 dark:text-gray-400"
          >
            <div className="flex items-center gap-2">
              <div className="h-2 w-2 rounded-full bg-green-500"></div>
              <span>99.9% Uptime</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="h-2 w-2 rounded-full bg-blue-500"></div>
              <span>Enterprise Security</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="h-2 w-2 rounded-full bg-purple-500"></div>
              <span>24/7 Support</span>
            </div>
          </motion.div>
        </motion.div>
      </div>

      {/* Floating Document Cards with better spacing */}
      <FloatingDocumentCard
        position={positions.card1}
        delay={0}
        title="Financial Report Q4"
        type="PDF"
      />

      <FloatingDocumentCard
        position={positions.card2}
        delay={1}
        title="Contract Agreement"
        type="DOCX"
      />

      <FloatingDocumentCard
        position={positions.card3}
        delay={2}
        title="Invoice Template"
        type="XLSX"
      />

      {/* Fourth card for larger screens */}
      {positions.card4 && (
        <FloatingDocumentCard
          position={positions.card4}
          delay={2.5}
          title="Analytics Data"
          type="CSV"
        />
      )}
    </section>
  );
};

export default Hero;
