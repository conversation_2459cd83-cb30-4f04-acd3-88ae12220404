{"name": "centris-web", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@hookform/resolvers": "^5.1.1", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-slot": "^1.2.3", "accept-language": "^3.0.20", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "country-flag-icons": "^1.5.19", "debug": "^4.4.1", "i18next": "^25.2.1", "i18next-browser-languagedetector": "^8.2.0", "i18next-resources-to-backend": "^1.2.1", "lucide-react": "^0.515.0", "motion": "^12.18.1", "next": "15.3.3", "next-themes": "^0.4.6", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.58.1", "react-i18next": "^15.5.3", "sonner": "^2.0.5", "tailwind-merge": "^3.3.1", "zod": "^3.25.67"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/debug": "^4.1.12", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-airbnb-base": "^15.0.0", "eslint-config-airbnb-typescript": "^18.0.0", "eslint-config-next": "15.3.3", "eslint-config-prettier": "^10.1.5", "eslint-plugin-import": "^2.31.0", "eslint-plugin-prettier": "^5.4.1", "prettier": "^3.5.3", "prettier-plugin-tailwindcss": "^0.6.12", "tailwindcss": "^4", "tw-animate-css": "^1.3.4", "typescript": "^5"}}