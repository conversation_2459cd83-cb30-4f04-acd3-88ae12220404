import '@/styles/globals.css';
import { geist<PERSON><PERSON>, geist<PERSON><PERSON> } from '@/styles/fonts';

import { BASE_URL } from '@/constants/metadata';

import type { Metadata } from 'next';

import { detectLanguageFromHeaders } from '@/lib/i18n/detector';
import { getServerTranslation } from '@/lib/i18n/server';
import { languageMetadata } from '@/lib/i18n/settings';

import AppProviders from '@/providers';

import Navbar from '@/components/navbar';
import Footer from '@/components/footer';
import MainContent from '@/components/main-content';

/**
 * Dynamic metadata based on detected language
 * SEO optimization with proper language alternates
 */
export async function generateMetadata(): Promise<Metadata> {
  const language = await detectLanguageFromHeaders();
  const { t } = await getServerTranslation(language, 'common');

  return {
    title: {
      template: `%s | ${t('site.title')}`,
      default: t('site.title'),
    },
    description: t('site.description'),

    // Icons
    icons: {
      icon: [
        { url: '/icon.png', type: 'image/png' },
        { url: '/favicon.ico', type: 'image/x-icon' },
      ],
      apple: [{ url: '/apple-icon.png', type: 'image/png' }],
    },

    // Critical for SEO: Language alternates for all supported languages
    alternates: {
      languages: {
        en: BASE_URL,
        cs: BASE_URL,
        'x-default': BASE_URL,
      },
    },

    // Open Graph with language info
    openGraph: {
      title: t('site.name'),
      description: t('site.description'),
      locale: language,
      alternateLocale: language === 'en' ? 'cs' : 'en',
      type: 'website',
    },

    // Twitter Cards
    twitter: {
      card: 'summary_large_image',
      title: t('site.name'),
      description: t('site.description'),
    },
  };
}

export default async function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  // Detect language from headers (middleware set it)
  const detectedLanguage = await detectLanguageFromHeaders();
  const languageMeta = languageMetadata[detectedLanguage];

  return (
    <html lang={detectedLanguage} dir={languageMeta.dir}>
      <head>
        {/* Hreflang meta tags for SEO */}
        <link rel="alternate" hrefLang="en" href={BASE_URL} />
        <link rel="alternate" hrefLang="cs" href={BASE_URL} />
        <link rel="alternate" hrefLang="x-default" href={BASE_URL} />
      </head>
      <body
        className={`${geistSans.variable} ${geistMono.variable} min-h-screen overflow-x-hidden bg-gray-50 antialiased transition-colors duration-500 dark:bg-gray-900`}
      >
        <AppProviders detectedLanguage={detectedLanguage}>
          {/* Navbar */}
          <Navbar />

          {/* Main content */}
          <MainContent>{children}</MainContent>

          {/* Footer */}
          <Footer />
        </AppProviders>
      </body>
    </html>
  );
}
