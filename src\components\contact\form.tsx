'use client';

import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { motion } from 'motion/react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { toast } from 'sonner';
import { createContactSchema, ContactFormData } from '@/schema/contact';
import { Send, Loader2 } from 'lucide-react';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Checkbox } from '@/components/ui/checkbox';
import { Button } from '@/components/ui/button';

const ContactForm = () => {
  const { t } = useTranslation('contact');

  const [isSubmitting, setIsSubmitting] = useState(false);

  // Create schema with current translations
  const contactSchema = createContactSchema(t);

  const form = useForm<ContactFormData>({
    resolver: zodR<PERSON>olver(contactSchema),
    defaultValues: {
      firstName: '',
      lastName: '',
      email: '',
      phone: '',
      company: '',
      message: '',
      consent: false,
    },
  });

  const onSubmit = async (data: ContactFormData) => {
    setIsSubmitting(true);

    try {
      // Simulate API call
      await new Promise((resolve) => {
        console.log(data);
        setTimeout(resolve, 2000);
      });

      // Show success toast
      toast.success(t('form.success.title'), {
        description: t('form.success.description'),
      });

      // Reset form
      form.reset();
    } catch (error) {
      // Show error toast
      toast.error(t('form.error.title'), {
        description: t('form.error.description'),
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, x: 50 }}
      animate={{ opacity: 1, x: 0 }}
      transition={{ duration: 0.8, delay: 0.3 }}
      whileHover={{
        y: -8,
        rotateX: -2,
        rotateY: -2,
        scale: 1.01,
      }}
      className="card-modern perspective-1000 transform-gpu p-8 will-change-transform"
      style={{
        transformStyle: 'preserve-3d',
      }}
    >
      <div className="mb-6">
        <h2 className="text-foreground mb-2 text-2xl font-bold">
          {t('form.title')}
        </h2>
        <div className="from-primary to-secondary h-1 w-16 rounded-full bg-gradient-to-r" />
      </div>

      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
          {/* First Name and Last Name Row */}
          <motion.div
            className="grid gap-4 sm:grid-cols-2"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.1 }}
          >
            <FormField
              control={form.control}
              name="firstName"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-foreground">
                    {t('form.fields.firstName.label')} *
                  </FormLabel>
                  <FormControl>
                    <Input
                      placeholder={t('form.fields.firstName.placeholder')}
                      className="focus:ring-primary/20 transition-all duration-200 focus:ring-2"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="lastName"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-foreground">
                    {t('form.fields.lastName.label')} *
                  </FormLabel>
                  <FormControl>
                    <Input
                      placeholder={t('form.fields.lastName.placeholder')}
                      className="focus:ring-primary/20 transition-all duration-200 focus:ring-2"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </motion.div>

          {/* Email and Phone Row */}
          <motion.div
            className="grid gap-4 sm:grid-cols-2"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.2 }}
          >
            <FormField
              control={form.control}
              name="email"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-foreground">
                    {t('form.fields.email.label')} *
                  </FormLabel>
                  <FormControl>
                    <Input
                      type="email"
                      placeholder={t('form.fields.email.placeholder')}
                      className="focus:ring-primary/20 transition-all duration-200 focus:ring-2"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="phone"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-foreground">
                    {t('form.fields.phone.label')} *
                  </FormLabel>
                  <FormControl>
                    <Input
                      type="tel"
                      placeholder={t('form.fields.phone.placeholder')}
                      className="focus:ring-primary/20 transition-all duration-200 focus:ring-2"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </motion.div>

          {/* Company Field */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.3 }}
          >
            <FormField
              control={form.control}
              name="company"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-foreground">
                    {t('form.fields.company.label')}
                  </FormLabel>
                  <FormControl>
                    <Input
                      placeholder={t('form.fields.company.placeholder')}
                      className="focus:ring-primary/20 transition-all duration-200 focus:ring-2"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </motion.div>

          {/* Message Field */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.4 }}
          >
            <FormField
              control={form.control}
              name="message"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-foreground">
                    {t('form.fields.message.label')} *
                  </FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder={t('form.fields.message.placeholder')}
                      className="focus:ring-primary/20 min-h-[120px] resize-none transition-all duration-200 focus:ring-2"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </motion.div>

          {/* Consent Checkbox */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.5 }}
          >
            <FormField
              control={form.control}
              name="consent"
              render={({ field }) => (
                <FormItem className="flex flex-row items-start space-y-0 space-x-3">
                  <FormControl>
                    <Checkbox
                      checked={field.value}
                      onCheckedChange={field.onChange}
                      className="mt-1"
                    />
                  </FormControl>
                  <div className="space-y-1 leading-none">
                    <FormLabel className="text-muted-foreground text-sm">
                      {t('form.fields.consent.label')} *
                    </FormLabel>
                    <FormMessage />
                  </div>
                </FormItem>
              )}
            />
          </motion.div>

          {/* Submit Button */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{
              duration: 0.5,
              delay: 0.6,
              type: 'spring',
              stiffness: 400,
              damping: 25,
            }}
            whileHover={{
              scale: 1.05,
              rotateX: 5,
              y: -2,
            }}
            whileTap={{ scale: 0.95 }}
          >
            <Button
              type="submit"
              disabled={isSubmitting}
              className="from-primary to-secondary text-primary-foreground w-full bg-gradient-to-r shadow-lg transition-all duration-200 hover:shadow-xl"
            >
              {isSubmitting ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  {t('form.submitting')}
                </>
              ) : (
                <>
                  <Send className="mr-2 h-4 w-4" />
                  {t('form.submit')}
                </>
              )}
            </Button>
          </motion.div>
        </form>
      </Form>
    </motion.div>
  );
};

export default ContactForm;
