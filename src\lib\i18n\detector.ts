import { NextRequest } from 'next/server';
import { cookies, headers } from 'next/headers';
import acceptLanguage from 'accept-language';
import { fallbackLng, languages, cookieName, type Language } from './settings';
import debug from 'debug';

const langLog = debug('i18n:detector:lang');

// Configuration of the accept-language parser
acceptLanguage.languages([...languages]);

/**
 * Server-side language detection for middleware
 * Priority: Cookie → Accept-Language → Fallback
 */
export async function detectLanguageFromRequest(
  request: NextRequest
): Promise<Language> {
  // 1. First, check the cookie from the previous visit
  const cookieValue = request.cookies.get(cookieName)?.value;
  if (cookieValue && languages.includes(cookieValue as Language)) {
    langLog(`🍪 Language detected from cookie: ${cookieValue}`);
    return cookieValue as Language;
  }

  // 2. Analyze the Accept-Language header from the browser
  const acceptLang = request.headers.get('accept-language');
  if (acceptLang) {
    const detected = acceptLanguage.get(acceptLang);
    if (detected && languages.includes(detected as Language)) {
      langLog(`🌐 Language detected from browser: ${detected}`);
      return detected as Language;
    }
  }

  // 3. Fallback to default language
  langLog(`⚡ Using fallback language: ${fallbackLng}`);
  return fallbackLng;
}

/**
 * Server Components language detection (Next.js headers)
 * Used in layout.tsx and server components
 *
 * 🔧 Safe for Next.js 15 build time
 * During the build process, cookies() and headers() are not available, so
 * we silently fall back to the fallback language without error messages
 */
export async function detectLanguageFromHeaders(): Promise<Language> {
  try {
    // Read cookie from server headers
    // This only works during runtime when we have a real HTTP request.
    const cookieStore = await cookies();
    const savedLanguage = cookieStore.get(cookieName)?.value;

    if (savedLanguage && languages.includes(savedLanguage as Language)) {
      return savedLanguage as Language;
    }

    // As a fallback, try the Accept-Language header
    const headersList = await headers();
    const acceptLang = headersList.get('accept-language');

    if (acceptLang) {
      const detected = acceptLanguage.get(acceptLang);
      if (detected && languages.includes(detected as Language)) {
        return detected as Language;
      }
    }
  } catch (error) {
    // 🔧 During the Next.js build process, we get here.
    // We don't log the error to the console, we just continue to a safe fallback.
    // The build process then runs without any problems.
  }

  // 🔧 Secure fallback for build time and runtime scenarios
  return fallbackLng;
}

/**
 * Use this function in components where you REALLY need accurate cookies
 * and can afford to export const dynamic = ‘force-dynamic’
 *
 * Examples of use:
 * - User dashboard pages
 * - Settings/preferences pages
 * - Personalized content
 */
export async function detectLanguageFromCookiesOnly(): Promise<Language> {
  try {
    const cookieStore = await cookies();
    const savedLanguage = cookieStore.get(cookieName)?.value;

    if (savedLanguage && languages.includes(savedLanguage as Language)) {
      return savedLanguage as Language;
    }
  } catch (error) {
    // This should not happen for force-dynamic pages,
    // but we have a fallback just in case.
    console.warn('Cookies not available, using fallback language');
  }

  return fallbackLng;
}

/**
 * Client-side language detection using the window object
 * Used during initialization of client components
 */
export function detectLanguageFromBrowser(): Language {
  if (typeof window === 'undefined') {
    return fallbackLng;
  }

  // Check localStorage as cache
  const stored = localStorage.getItem(cookieName);
  if (stored && languages.includes(stored as Language)) {
    return stored as Language;
  }

  // Analyze navigator.languages
  const browserLanguages = navigator.languages || [navigator.language];

  for (const browserLang of browserLanguages) {
    const lang = browserLang.split('-')[0]; // "en-US" → "en"
    if (languages.includes(lang as Language)) {
      return lang as Language;
    }
  }

  return fallbackLng;
}
