/**
 * Footer sections configuration
 * This file contains the configuration for footer sections with their links
 */

export interface FooterSectionLink {
  key: string;
  href: string;
  external?: boolean;
  target?: '_blank' | '_self';
}

export interface FooterSection {
  titleKey: string;
  links: FooterSectionLink[];
}

/**
 * About section links configuration
 */
export const aboutSectionLinks: FooterSectionLink[] = [
  {
    key: 'about-us',
    href: '/about',
    external: false,
  },
  {
    key: 'contact',
    href: '/contact',
    external: false,
  },
  {
    key: 'faq',
    href: '/faq',
    external: false,
  },
];

/**
 * Products section links configuration
 * All product links follow the pattern /products/{key}
 */
export const productSectionLinks: FooterSectionLink[] = [
  {
    key: 'mailroom',
    href: '/products/mailroom',
    external: false,
  },
  {
    key: 'received-invoices',
    href: '/products/received-invoices',
    external: false,
  },
  {
    key: 'issued-invoices',
    href: '/products/issued-invoices',
    external: false,
  },
  {
    key: 'contracts',
    href: '/products/contracts',
    external: false,
  },
  {
    key: 'requisitions',
    href: '/products/requisitions',
    external: false,
  },
  {
    key: 'directives',
    href: '/products/directives',
    external: false,
  },
  {
    key: 'digital-archive',
    href: '/products/digital-archive',
    external: false,
  },
  {
    key: 'identity-management',
    href: '/products/identity-management',
    external: false,
  },
];

/**
 * Complete footer sections configuration
 */
export const footerSections = {
  about: {
    titleKey: 'footer.sections.about.title',
    links: aboutSectionLinks,
  },
  products: {
    titleKey: 'footer.sections.products.title',
    links: productSectionLinks,
  },
} as const;

/**
 * Helper function to get link attributes based on configuration
 */
export const getSectionLinkAttributes = (link: FooterSectionLink) => {
  const attributes: Record<string, string> = {
    href: link.href,
  };

  if (link.external) {
    attributes.target = link.target || '_blank';
    attributes.rel = 'noopener noreferrer';
  }

  return attributes;
};
