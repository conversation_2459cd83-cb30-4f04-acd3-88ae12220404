/**
 * Central configuration for i18next according to the official documentation at i18next.com.
 * All constants and types are here for consistency.
 */

// Supported languages - add more as needed
export const fallbackLng = 'en' as const;
export const languages = [fallbackLng, 'cs'] as const;
export type Language = (typeof languages)[number];

// Namespace configuration for translation organization
export const defaultNS = 'common' as const;
export const namespaces = ['common', 'products', 'contact'] as const;
export type Namespace = (typeof namespaces)[number];

// Cookie and header configuration for persistence
export const cookieName = 'i18next-lng';
export const headerName = 'x-i18next-lng';

// Import metadata from constants
import { LANGUAGE_METADATA } from '@/constants/languages';

// Export for backward compatibility if needed
export const languageMetadata = LANGUAGE_METADATA;

// Helper function for validation
export function isValidLanguage(lng: string): lng is Language {
  return languages.includes(lng as Language);
}

export function isValidNamespace(ns: string): ns is Namespace {
  return namespaces.includes(ns as Namespace);
}

// Constants for all literal values
const I18N_LOAD_STRATEGY = 'currentOnly' as const;

// Default configuration for i18next instances
export function getI18nextOptions(
  lng: Language = fallbackLng,
  ns: Namespace[] = [defaultNS]
) {
  return {
    lng,
    fallbackLng,
    supportedLngs: [...languages],
    defaultNS,
    ns,

    // Interpolation settings (important for React)
    interpolation: {
      escapeValue: false, // React already escapes values
    },

    // Checking translations in development
    debug: process.env.NODE_ENV === 'development',

    // Formatting for consistency
    returnEmptyString: false,
    returnNull: false,

    // Optimization
    load: I18N_LOAD_STRATEGY, // "en-US" → "en"
    cleanCode: true,
  };
}
