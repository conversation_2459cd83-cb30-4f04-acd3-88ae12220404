import React from 'react';
import Image from 'next/image';

import { APP_NAME } from '@/constants';

type Props = {
  className?: string;
  priority?: boolean;
  alt?: string;
  height?: {
    mobile?: number;
    tablet?: number;
    desktop?: number;
  };
};

const Brand = ({
  className = '',
  priority = false,
  alt = APP_NAME,
  height = {
    mobile: 40,
    tablet: 48,
    desktop: 56,
  },
}: Props) => {
  return (
    <>
      {/* Mobile logo (logo-mobile.svg) - Tailwind CSS v4 responsive design */}
      <Image
        src="/img/brand/logo-mobile.svg"
        alt={alt}
        width={120}
        height={height.mobile}
        priority={priority}
        sizes="120px"
        unoptimized
        className={`block h-auto max-h-10 w-auto object-contain transition-all duration-300 ease-out md:hidden ${className} `}
      />

      {/* Tablet/Desktop logo (logo.svg) - Tailwind CSS v4 breakpoints */}
      <Image
        src="/img/brand/logo.svg"
        alt={alt}
        width={160}
        height={height.tablet}
        priority={priority}
        sizes="(min-width: 768px) and (max-width: 1023px) 160px, 180px"
        unoptimized
        className={`hidden h-auto max-h-12 w-auto object-contain transition-all duration-300 ease-out hover:scale-105 focus:scale-105 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 focus:outline-none md:block lg:max-h-14 xl:max-h-16 ${className} `}
      />
    </>
  );
};

export default Brand;
