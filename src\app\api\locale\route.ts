import { NextRequest, NextResponse } from 'next/server';
import { cookies } from 'next/headers';
import { languages, cookieName, type Language } from '@/lib/i18n/settings';
import debug from 'debug';

const langLog = debug('api:lang');

/**
 * API route for synchronizing language preferences with the server
 * Called from the client-side language switcher for persistence
 */
export async function POST(request: NextRequest) {
  try {
    const { language } = await request.json();

    // Language validation
    if (!language || !languages.includes(language as Language)) {
      return NextResponse.json(
        {
          error: 'Invalid language',
          received: language,
          supported: languages,
        },
        { status: 400 }
      );
    }

    langLog(`🍪 Setting language cookie to: ${language}`);

    // Set HTTP cookie for server-side access
    const cookieStore = await cookies();
    cookieStore.set(cookieName, language, {
      httpOnly: false, // Must be accessible from JavaScript
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'lax',
      maxAge: 60 * 60 * 24 * 365, // 1 year
      path: '/',
    });

    return NextResponse.json({
      success: true,
      language,
      message: `Language preference saved: ${language}`,
    });
  } catch (error) {
    console.error('Language API error:', error);
    return NextResponse.json(
      { error: 'Failed to save language preference' },
      { status: 500 }
    );
  }
}

/**
 * GET endpoint for determining the current language preference
 * Useful for debugging or external tools
 */
export async function GET() {
  try {
    const cookieStore = await cookies();
    const currentLanguage = cookieStore.get(cookieName)?.value || 'en';

    return NextResponse.json({
      currentLanguage,
      supportedLanguages: languages,
      cookieName,
    });
  } catch (error) {
    console.error('Language GET API error:', error);
    return NextResponse.json(
      { error: 'Failed to get language preference' },
      { status: 500 }
    );
  }
}
