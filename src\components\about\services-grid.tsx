'use client';

import React from 'react';
import { useTranslation, Trans } from 'react-i18next';
import { motion } from 'motion/react';
import { FileText, Workflow, Archive, Zap, Check } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import GradientHighlighter from '@/components/gradient-highlighter';

const ServicesGrid = () => {
  const { t } = useTranslation('about');

  const services = [
    {
      key: 'processing',
      icon: FileText,
      color: 'text-blue-600 dark:text-blue-400',
      bgColor: 'bg-blue-100 dark:bg-blue-900/30',
      gradient: 'from-blue-500/20 to-blue-600/20',
    },
    {
      key: 'workflow',
      icon: Workflow,
      color: 'text-cyan-600 dark:text-cyan-400',
      bgColor: 'bg-cyan-100 dark:bg-cyan-900/30',
      gradient: 'from-cyan-500/20 to-cyan-600/20',
    },
    {
      key: 'archive',
      icon: Archive,
      color: 'text-primary dark:text-primary',
      bgColor: 'bg-primary/10 dark:bg-primary/20',
      gradient: 'from-primary/20 to-primary/30',
    },
    {
      key: 'integration',
      icon: Zap,
      color: 'text-secondary dark:text-secondary',
      bgColor: 'bg-secondary/10 dark:bg-secondary/20',
      gradient: 'from-secondary/20 to-secondary/30',
    },
  ];

  return (
    <section className="from-muted/30 to-background bg-gradient-to-b px-4 py-20">
      <div className="mx-auto max-w-7xl">
        {/* Section header */}
        <div className="mb-16 text-center">
          <Trans
            i18nKey="services.title"
            ns="about"
            parent={motion.h2}
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true, margin: '-100px' }}
            transition={{ duration: 0.8 }}
            className="mb-4 text-3xl font-bold text-gray-900 sm:text-4xl md:text-5xl dark:text-white"
            components={{
              highlighted: <GradientHighlighter />,
            }}
          />
          <motion.p
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true, margin: '-100px' }}
            transition={{ duration: 0.8, delay: 0.2 }}
            className="text-primary mb-4 text-xl font-semibold"
          >
            {t('services.subtitle')}
          </motion.p>
          <motion.p
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true, margin: '-100px' }}
            transition={{ duration: 0.8, delay: 0.4 }}
            className="mx-auto max-w-3xl text-lg text-gray-700 dark:text-gray-300"
          >
            {t('services.description')}
          </motion.p>
        </div>

        {/* Services grid */}
        <div className="grid gap-8 md:grid-cols-2 lg:grid-cols-2 xl:grid-cols-2">
          {services.map((service, index) => {
            const IconComponent = service.icon;
            return (
              <motion.div
                key={service.key}
                initial={{ opacity: 0, y: 50, scale: 0.95 }}
                whileInView={{ opacity: 1, y: 0, scale: 1 }}
                viewport={{ once: true, margin: '-100px' }}
                transition={{ duration: 0.8, delay: index * 0.2 }}
                whileHover={{ y: -8, scale: 1.02 }}
                className="group"
              >
                <Card className="border-border/50 bg-card/80 group-hover:border-primary/30 h-full overflow-hidden backdrop-blur-sm transition-all duration-500 hover:shadow-2xl">
                  {/* Card header with icon */}
                  <div
                    className={`relative bg-gradient-to-br p-6 pb-4 ${service.gradient}`}
                  >
                    <div className="flex items-start gap-4">
                      <div
                        className={`h-16 w-16 rounded-2xl ${service.bgColor} flex items-center justify-center transition-all duration-300 group-hover:scale-110 group-hover:rotate-3`}
                      >
                        <IconComponent className={`h-8 w-8 ${service.color}`} />
                      </div>
                      <div className="flex-1">
                        <h3 className="mb-2 text-2xl font-bold text-gray-900 dark:text-white">
                          {t(`services.items.${service.key}.title`)}
                        </h3>
                        <p className="leading-relaxed text-gray-700 dark:text-gray-300">
                          {t(`services.items.${service.key}.description`)}
                        </p>
                      </div>
                    </div>
                  </div>

                  {/* Card content with features */}
                  <CardContent className="p-6 pt-4">
                    <div className="space-y-3">
                      <h4 className="mb-3 text-sm font-semibold tracking-wide text-gray-600 uppercase dark:text-gray-400">
                        Key Features
                      </h4>
                      <div className="grid gap-2">
                        {(
                          t(`services.items.${service.key}.features`, {
                            returnObjects: true,
                          }) as string[]
                        ).map((feature, featureIndex) => (
                          <motion.div
                            key={featureIndex}
                            initial={{ opacity: 0, x: -20 }}
                            whileInView={{ opacity: 1, x: 0 }}
                            viewport={{ once: true }}
                            transition={{
                              duration: 0.5,
                              delay: index * 0.2 + featureIndex * 0.1,
                            }}
                            className="group/feature flex items-center gap-3"
                          >
                            <div className="from-primary to-secondary flex h-5 w-5 flex-shrink-0 items-center justify-center rounded-full bg-gradient-to-r">
                              <Check className="h-3 w-3 text-white" />
                            </div>
                            <span className="text-gray-700 transition-colors duration-200 group-hover/feature:text-gray-900 dark:text-gray-300 dark:group-hover/feature:text-white">
                              {feature}
                            </span>
                          </motion.div>
                        ))}
                      </div>
                    </div>

                    {/* Service badge */}
                    <div className="border-border/50 mt-6 border-t pt-4">
                      <Badge
                        variant="outline"
                        className={`${service.color} border-current bg-current/10 transition-colors duration-300 hover:bg-current/20`}
                      >
                        Professional Service
                      </Badge>
                    </div>
                  </CardContent>

                  {/* Hover effect overlay */}
                  <div className="from-primary/5 to-secondary/5 pointer-events-none absolute inset-0 bg-gradient-to-br opacity-0 transition-opacity duration-500 group-hover:opacity-100" />
                </Card>
              </motion.div>
            );
          })}
        </div>

        {/* Bottom CTA section */}
        <motion.div
          initial={{ opacity: 0, y: 50 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true, margin: '-100px' }}
          transition={{ duration: 0.8, delay: 0.6 }}
          className="mt-16 text-center"
        >
          <div className="from-primary/10 to-secondary/10 border-primary/20 mx-auto max-w-2xl rounded-2xl border bg-gradient-to-br p-8 backdrop-blur-sm">
            <h3 className="mb-4 text-2xl font-bold text-gray-900 dark:text-white">
              Ready to Transform Your Document Processing?
            </h3>
            <p className="mb-6 text-gray-700 dark:text-gray-300">
              Discover how our comprehensive solutions can streamline your
              business operations and boost productivity.
            </p>
            <div className="flex flex-col justify-center gap-4 sm:flex-row">
              <Badge
                variant="outline"
                className="text-primary border-primary/30 bg-primary/10"
              >
                Enterprise Ready
              </Badge>
              <Badge
                variant="outline"
                className="text-secondary border-secondary/30 bg-secondary/10"
              >
                Scalable Solutions
              </Badge>
              <Badge
                variant="outline"
                className="border-cyan-600/30 bg-cyan-600/10 text-cyan-600"
              >
                24/7 Support
              </Badge>
            </div>
          </div>
        </motion.div>
      </div>
    </section>
  );
};

export default ServicesGrid;
